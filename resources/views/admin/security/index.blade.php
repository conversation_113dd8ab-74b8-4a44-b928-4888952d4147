@extends('admin.layout')

@section('title', 'Security Dashboard')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-white">Security Dashboard</h1>
            <p class="text-gray-400">Monitor threats, manage security events, and review content moderation</p>
        </div>
        <div class="flex space-x-3">
            <select class="bg-gray-800 border border-gray-600 text-white rounded-lg px-4 py-2" onchange="window.location.href='?period='+this.value">
                <option value="today" {{ $period == 'today' ? 'selected' : '' }}>Today</option>
                <option value="week" {{ $period == 'week' ? 'selected' : '' }}>This Week</option>
                <option value="month" {{ $period == 'month' ? 'selected' : '' }}>This Month</option>
            </select>
            <a href="{{ route('admin.security.settings') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                Settings
            </a>
        </div>
    </div>

    <!-- Security Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-400 text-sm">Security Events</p>
                    <p class="text-2xl font-bold text-white">{{ $securityMetrics['total_events'] ?? 0 }}</p>
                </div>
                <div class="bg-red-500/20 p-3 rounded-lg">
                    <svg class="w-6 h-6 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                </div>
            </div>
            <div class="mt-2">
                <span class="text-sm text-gray-400">{{ $securityMetrics['critical_events'] ?? 0 }} critical</span>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-400 text-sm">Content Moderated</p>
                    <p class="text-2xl font-bold text-white">{{ $moderationStats['total_moderated'] ?? 0 }}</p>
                </div>
                <div class="bg-yellow-500/20 p-3 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clip-rule="evenodd"/>
                    </svg>
                </div>
            </div>
            <div class="mt-2">
                <span class="text-sm text-gray-400">{{ $moderationStats['pending'] ?? 0 }} pending review</span>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-400 text-sm">Blocked IPs</p>
                    <p class="text-2xl font-bold text-white">{{ count($suspiciousIPs) }}</p>
                </div>
                <div class="bg-purple-500/20 p-3 rounded-lg">
                    <svg class="w-6 h-6 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                    </svg>
                </div>
            </div>
            <div class="mt-2">
                <span class="text-sm text-gray-400">Auto-blocked</span>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-400 text-sm">AI Accuracy</p>
                    <p class="text-2xl font-bold text-white">{{ number_format($autoModerationStats['accuracy_rate'], 1) }}%</p>
                </div>
                <div class="bg-green-500/20 p-3 rounded-lg">
                    <svg class="w-6 h-6 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                </div>
            </div>
            <div class="mt-2">
                <span class="text-sm text-gray-400">{{ $autoModerationStats['content_blocked'] }} blocked</span>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-gray-800 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-white mb-4">Critical Events</h3>
            <div class="space-y-3">
                @forelse($criticalEvents as $event)
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <div>
                        <p class="text-white text-sm">{{ $event->description }}</p>
                        <p class="text-gray-400 text-xs">{{ $event->created_at->diffForHumans() }}</p>
                    </div>
                    <span class="px-2 py-1 bg-red-500/20 text-red-400 text-xs rounded">{{ $event->severity }}</span>
                </div>
                @empty
                <p class="text-gray-400 text-sm">No critical events</p>
                @endforelse
            </div>
            <div class="mt-4">
                <a href="{{ route('admin.security.events') }}" class="text-blue-400 hover:text-blue-300 text-sm">View all events →</a>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-white mb-4">Pending Moderation</h3>
            <div class="space-y-3">
                @forelse($pendingModeration as $moderation)
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <div>
                        <p class="text-white text-sm">{{ $moderation->content_type }} content</p>
                        <p class="text-gray-400 text-xs">AI Score: {{ number_format($moderation->ai_score * 100, 1) }}%</p>
                    </div>
                    <div class="flex space-x-2">
                        <form method="POST" action="{{ route('admin.security.moderation.approve', $moderation) }}" class="inline">
                            @csrf
                            <button type="submit" class="text-green-400 hover:text-green-300 text-xs">Approve</button>
                        </form>
                        <button class="text-red-400 hover:text-red-300 text-xs">Reject</button>
                    </div>
                </div>
                @empty
                <p class="text-gray-400 text-sm">No pending moderation</p>
                @endforelse
            </div>
            <div class="mt-4">
                <a href="{{ route('admin.security.moderation') }}" class="text-blue-400 hover:text-blue-300 text-sm">View moderation queue →</a>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-white mb-4">Threat Analysis</h3>
            <div class="space-y-3">
                @forelse($topThreats as $threat)
                <div class="flex items-center justify-between">
                    <span class="text-white text-sm">{{ $threat->event_type }}</span>
                    <span class="text-gray-400 text-sm">{{ $threat->count }}</span>
                </div>
                @empty
                <p class="text-gray-400 text-sm">No threats detected</p>
                @endforelse
            </div>
            <div class="mt-4">
                <a href="{{ route('admin.security.analytics') }}" class="text-blue-400 hover:text-blue-300 text-sm">View analytics →</a>
            </div>
        </div>
    </div>

    <!-- Navigation Links -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <a href="{{ route('admin.security.events') }}" class="bg-gray-800 hover:bg-gray-700 p-4 rounded-lg text-center transition-colors">
            <div class="text-white font-semibold">Security Events</div>
            <div class="text-gray-400 text-sm">View & manage events</div>
        </a>
        <a href="{{ route('admin.security.moderation') }}" class="bg-gray-800 hover:bg-gray-700 p-4 rounded-lg text-center transition-colors">
            <div class="text-white font-semibold">Content Moderation</div>
            <div class="text-gray-400 text-sm">Review flagged content</div>
        </a>
        <a href="{{ route('admin.security.blocked-ips') }}" class="bg-gray-800 hover:bg-gray-700 p-4 rounded-lg text-center transition-colors">
            <div class="text-white font-semibold">Blocked IPs</div>
            <div class="text-gray-400 text-sm">Manage IP blocks</div>
        </a>
        <a href="{{ route('admin.security.suspended-users') }}" class="bg-gray-800 hover:bg-gray-700 p-4 rounded-lg text-center transition-colors">
            <div class="text-white font-semibold">Suspended Users</div>
            <div class="text-gray-400 text-sm">User suspensions</div>
        </a>
    </div>
</div>
@endsection
