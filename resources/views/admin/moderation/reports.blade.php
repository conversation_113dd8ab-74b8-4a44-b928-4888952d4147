@extends('admin.layout')

@section('title', 'Content Reports')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-white">Content Reports</h1>
            <p class="text-gray-400">Review and manage user-reported content</p>
        </div>
        <div class="flex space-x-3">
            <select class="bg-gray-800 border border-gray-600 text-white rounded-lg px-4 py-2" onchange="filterReports(this.value)">
                <option value="all">All Reports</option>
                <option value="pending">Pending</option>
                <option value="reviewed">Reviewed</option>
                <option value="dismissed">Dismissed</option>
            </select>
            <select class="bg-gray-800 border border-gray-600 text-white rounded-lg px-4 py-2" onchange="filterByType(this.value)">
                <option value="all">All Types</option>
                <option value="spam">Spam</option>
                <option value="harassment">Harassment</option>
                <option value="inappropriate">Inappropriate</option>
                <option value="copyright">Copyright</option>
                <option value="other">Other</option>
            </select>
        </div>
    </div>

    <!-- Reports Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-400 text-sm">Total Reports</p>
                    <p class="text-2xl font-bold text-white">{{ $reports->total() ?? 0 }}</p>
                </div>
                <div class="bg-blue-500/20 p-3 rounded-lg">
                    <svg class="w-6 h-6 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-400 text-sm">Pending Review</p>
                    <p class="text-2xl font-bold text-white">{{ $pendingCount ?? 0 }}</p>
                </div>
                <div class="bg-yellow-500/20 p-3 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-400 text-sm">Resolved Today</p>
                    <p class="text-2xl font-bold text-white">{{ $resolvedToday ?? 0 }}</p>
                </div>
                <div class="bg-green-500/20 p-3 rounded-lg">
                    <svg class="w-6 h-6 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-400 text-sm">Response Time</p>
                    <p class="text-2xl font-bold text-white">{{ $avgResponseTime ?? '0' }}h</p>
                </div>
                <div class="bg-purple-500/20 p-3 rounded-lg">
                    <svg class="w-6 h-6 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Reports Table -->
    <div class="bg-gray-800 rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Content</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Reporter</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-700">
                    @forelse($reports ?? [] as $report)
                    <tr class="hover:bg-gray-700">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div>
                                    <div class="text-sm font-medium text-white">
                                        {{ Str::limit($report->content ?? 'Content not available', 50) }}
                                    </div>
                                    <div class="text-sm text-gray-400">
                                        {{ $report->content_type ?? 'Unknown' }} #{{ $report->content_id ?? 'N/A' }}
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-white">{{ $report->reporter->name ?? 'Anonymous' }}</div>
                            <div class="text-sm text-gray-400">{{ $report->reporter->email ?? 'No email' }}</div>
                        </td>
                        <td class="px-6 py-4">
                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                {{ ($report->type ?? '') == 'spam' ? 'bg-red-500/20 text-red-400' : '' }}
                                {{ ($report->type ?? '') == 'harassment' ? 'bg-orange-500/20 text-orange-400' : '' }}
                                {{ ($report->type ?? '') == 'inappropriate' ? 'bg-yellow-500/20 text-yellow-400' : '' }}
                                {{ ($report->type ?? '') == 'copyright' ? 'bg-purple-500/20 text-purple-400' : '' }}
                                {{ ($report->type ?? '') == 'other' ? 'bg-gray-500/20 text-gray-400' : '' }}">
                                {{ ucfirst($report->type ?? 'Unknown') }}
                            </span>
                        </td>
                        <td class="px-6 py-4">
                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                {{ ($report->status ?? '') == 'pending' ? 'bg-yellow-500/20 text-yellow-400' : '' }}
                                {{ ($report->status ?? '') == 'reviewed' ? 'bg-green-500/20 text-green-400' : '' }}
                                {{ ($report->status ?? '') == 'dismissed' ? 'bg-gray-500/20 text-gray-400' : '' }}">
                                {{ ucfirst($report->status ?? 'Pending') }}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-400">
                            {{ $report->created_at ? $report->created_at->format('M j, Y g:i A') : 'Unknown' }}
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex space-x-2">
                                <button onclick="viewReport({{ $report->id ?? 0 }})" class="text-blue-400 hover:text-blue-300 text-sm">
                                    View
                                </button>
                                @if(($report->status ?? '') == 'pending')
                                <button onclick="approveReport({{ $report->id ?? 0 }})" class="text-green-400 hover:text-green-300 text-sm">
                                    Approve
                                </button>
                                <button onclick="dismissReport({{ $report->id ?? 0 }})" class="text-red-400 hover:text-red-300 text-sm">
                                    Dismiss
                                </button>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center">
                            <svg class="w-12 h-12 text-gray-600 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                            </svg>
                            <h3 class="text-lg font-semibold text-white mb-2">No Reports Found</h3>
                            <p class="text-gray-400">No content reports match your current filters</p>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    @if(isset($reports) && $reports->hasPages())
    <div class="flex justify-center">
        {{ $reports->links() }}
    </div>
    @endif
</div>

<!-- Report Detail Modal -->
<div id="reportModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-gray-800 rounded-lg p-6 w-full max-w-2xl">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold text-white">Report Details</h2>
                <button onclick="closeReportModal()" class="text-gray-400 hover:text-white">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                </button>
            </div>

            <div id="reportContent" class="space-y-4">
                <!-- Report details will be loaded here -->
            </div>

            <div class="flex justify-end space-x-3 mt-6">
                <button onclick="closeReportModal()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                    Close
                </button>
                <button onclick="takeAction()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                    Take Action
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function filterReports(status) {
    const url = new URL(window.location);
    url.searchParams.set('status', status);
    window.location.href = url.toString();
}

function filterByType(type) {
    const url = new URL(window.location);
    url.searchParams.set('type', type);
    window.location.href = url.toString();
}

function viewReport(id) {
    // Implementation for viewing report details
    document.getElementById('reportModal').classList.remove('hidden');
    console.log('View report:', id);
}

function closeReportModal() {
    document.getElementById('reportModal').classList.add('hidden');
}

function approveReport(id) {
    if (confirm('Are you sure you want to approve this report?')) {
        // Implementation for approving report
        console.log('Approve report:', id);
    }
}

function dismissReport(id) {
    if (confirm('Are you sure you want to dismiss this report?')) {
        // Implementation for dismissing report
        console.log('Dismiss report:', id);
    }
}

function takeAction() {
    // Implementation for taking action on report
    console.log('Take action on report');
}
</script>
@endsection
