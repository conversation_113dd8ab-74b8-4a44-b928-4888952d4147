@extends('layouts.admin')

@section('title', 'Content Moderation - Confessions')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Content Moderation - Confessions</h1>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#bulkActionModal">
                        <i class="fas fa-tasks"></i> Bulk Actions
                    </button>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Confessions</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total'] ?? 0 }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-comments fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Flagged</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['flagged'] ?? 0 }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-flag fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-danger shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Reported</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['reported'] ?? 0 }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">NSFW Content</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['nsfw'] ?? 0 }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-eye-slash fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.moderation.confessions') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <select name="filter" class="form-control">
                                    <option value="">All Confessions</option>
                                    <option value="reported" {{ request('filter') == 'reported' ? 'selected' : '' }}>Reported</option>
                                    <option value="flagged" {{ request('filter') == 'flagged' ? 'selected' : '' }}>Flagged</option>
                                    <option value="nsfw" {{ request('filter') == 'nsfw' ? 'selected' : '' }}>NSFW</option>
                                    <option value="ai" {{ request('filter') == 'ai' ? 'selected' : '' }}>AI Generated</option>
                                    <option value="high_engagement" {{ request('filter') == 'high_engagement' ? 'selected' : '' }}>High Engagement</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="text" name="search" class="form-control" placeholder="Search content..." value="{{ request('search') }}">
                            </div>
                            <div class="col-md-3">
                                <select name="sort" class="form-control">
                                    <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>Newest First</option>
                                    <option value="oldest" {{ request('sort') == 'oldest' ? 'selected' : '' }}>Oldest First</option>
                                    <option value="most_reported" {{ request('sort') == 'most_reported' ? 'selected' : '' }}>Most Reported</option>
                                    <option value="most_liked" {{ request('sort') == 'most_liked' ? 'selected' : '' }}>Most Liked</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="{{ route('admin.moderation.confessions') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Confessions Table -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Confessions</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="confessionsTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="selectAll"></th>
                                    <th>ID</th>
                                    <th>Content Preview</th>
                                    <th>User</th>
                                    <th>Category</th>
                                    <th>Status</th>
                                    <th>Engagement</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($confessions as $confession)
                                <tr class="{{ $confession->is_flagged ? 'table-warning' : '' }}">
                                    <td><input type="checkbox" name="confession_ids[]" value="{{ $confession->id }}" class="confession-checkbox"></td>
                                    <td>{{ $confession->id }}</td>
                                    <td>
                                        <div class="content-preview">
                                            {{ Str::limit($confession->content, 100) }}
                                            @if($confession->is_nsfw)
                                                <span class="badge badge-danger ml-2">NSFW</span>
                                            @endif
                                            @if($confession->is_ai_generated)
                                                <span class="badge badge-info ml-2">AI</span>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        @if($confession->is_anonymous)
                                            <span class="text-muted">Anonymous</span>
                                        @else
                                            <a href="{{ route('admin.users.show', $confession->user) }}">
                                                {{ $confession->user->name }}
                                            </a>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge badge-secondary">{{ $confession->category ?? 'General' }}</span>
                                    </td>
                                    <td>
                                        @if($confession->is_flagged)
                                            <span class="badge badge-warning">Flagged</span>
                                        @elseif($confession->is_hidden)
                                            <span class="badge badge-dark">Hidden</span>
                                        @else
                                            <span class="badge badge-success">Active</span>
                                        @endif
                                        
                                        @if($confession->reports_count > 0)
                                            <span class="badge badge-danger">{{ $confession->reports_count }} Reports</span>
                                        @endif
                                    </td>
                                    <td>
                                        <small>
                                            <i class="fas fa-heart text-danger"></i> {{ $confession->likes_count }}<br>
                                            <i class="fas fa-eye text-info"></i> {{ $confession->views }}<br>
                                            <i class="fas fa-bookmark text-warning"></i> {{ $confession->bookmark_count }}
                                        </small>
                                    </td>
                                    <td>{{ $confession->created_at->format('M j, Y H:i') }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.moderation.confession', $confession) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            @if(!$confession->is_flagged)
                                                <form method="POST" action="{{ route('admin.moderation.confession.update', $confession) }}" style="display: inline;">
                                                    @csrf
                                                    @method('PATCH')
                                                    <input type="hidden" name="action" value="flag">
                                                    <button type="submit" class="btn btn-sm btn-warning" title="Flag">
                                                        <i class="fas fa-flag"></i>
                                                    </button>
                                                </form>
                                            @endif
                                            
                                            @if(!$confession->is_hidden)
                                                <form method="POST" action="{{ route('admin.moderation.confession.update', $confession) }}" style="display: inline;">
                                                    @csrf
                                                    @method('PATCH')
                                                    <input type="hidden" name="action" value="hide">
                                                    <button type="submit" class="btn btn-sm btn-secondary" title="Hide">
                                                        <i class="fas fa-eye-slash"></i>
                                                    </button>
                                                </form>
                                            @endif
                                            
                                            <button type="button" class="btn btn-sm btn-danger" onclick="deleteConfession({{ $confession->id }})" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="9" class="text-center">No confessions found.</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    @if($confessions->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $confessions->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Action Modal -->
<div class="modal fade" id="bulkActionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Actions</h5>
                <button type="button" class="close" data-bs-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ route('admin.moderation.confessions.bulk') }}" id="bulkActionForm">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="bulkAction">Action</label>
                        <select name="action" id="bulkAction" class="form-control" required>
                            <option value="">Select Action</option>
                            <option value="approve">Approve</option>
                            <option value="flag">Flag</option>
                            <option value="hide">Hide</option>
                            <option value="mark_nsfw">Mark as NSFW</option>
                            <option value="delete">Delete</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="bulkReason">Reason (Optional)</label>
                        <textarea name="reason" id="bulkReason" class="form-control" rows="3" placeholder="Provide a reason for this action..."></textarea>
                    </div>
                    <div id="selectedCount" class="alert alert-info">
                        No confessions selected
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="bulkActionSubmit" disabled>Apply Action</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Deletion</h5>
                <button type="button" class="close" data-bs-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" id="deleteForm">
                @csrf
                @method('PATCH')
                <input type="hidden" name="action" value="delete">
                <div class="modal-body">
                    <p>Are you sure you want to delete this confession? This action cannot be undone.</p>
                    <div class="form-group">
                        <label for="deleteReason">Reason for deletion</label>
                        <textarea name="reason" id="deleteReason" class="form-control" rows="3" placeholder="Provide a reason for deletion..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete Confession</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.confession-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateSelectedCount();
});

// Update selected count
function updateSelectedCount() {
    const selected = document.querySelectorAll('.confession-checkbox:checked');
    const count = selected.length;
    const countElement = document.getElementById('selectedCount');
    const submitButton = document.getElementById('bulkActionSubmit');
    
    if (count > 0) {
        countElement.textContent = `${count} confession(s) selected`;
        countElement.className = 'alert alert-success';
        submitButton.disabled = false;
    } else {
        countElement.textContent = 'No confessions selected';
        countElement.className = 'alert alert-info';
        submitButton.disabled = true;
    }
}

// Add event listeners to checkboxes
document.querySelectorAll('.confession-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateSelectedCount);
});

// Bulk action form submission
document.getElementById('bulkActionForm').addEventListener('submit', function(e) {
    const selected = document.querySelectorAll('.confession-checkbox:checked');
    
    // Add selected IDs to form
    selected.forEach(checkbox => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'confession_ids[]';
        input.value = checkbox.value;
        this.appendChild(input);
    });
});

// Delete confession function
function deleteConfession(confessionId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const form = document.getElementById('deleteForm');
    form.action = `/admin/moderation/confessions/${confessionId}`;
    modal.show();
}
</script>
@endsection
