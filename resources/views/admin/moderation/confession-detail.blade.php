@extends('layouts.admin')

@section('title', 'Confession Details - Moderation')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Confession Details</h1>
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.moderation.confessions') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Confession Content -->
                <div class="col-lg-8">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">Confession #{{ $confession->id }}</h6>
                            <div>
                                @if($confession->is_flagged)
                                    <span class="badge badge-warning">Flagged</span>
                                @endif
                                @if($confession->is_hidden)
                                    <span class="badge badge-dark">Hidden</span>
                                @endif
                                @if($confession->is_nsfw)
                                    <span class="badge badge-danger">NSFW</span>
                                @endif
                                @if($confession->is_ai_generated)
                                    <span class="badge badge-info">AI Generated</span>
                                @endif
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="confession-content mb-4">
                                <p class="lead">{{ $confession->content }}</p>
                                
                                @if($confession->image)
                                    <div class="mt-3">
                                        <img src="{{ Storage::url($confession->image) }}" alt="Confession Image" class="img-fluid rounded" style="max-height: 400px;">
                                    </div>
                                @endif
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Metadata</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>Category:</strong> {{ $confession->category ?? 'General' }}</li>
                                        <li><strong>Anonymous:</strong> {{ $confession->is_anonymous ? 'Yes' : 'No' }}</li>
                                        <li><strong>Friends Only:</strong> {{ $confession->friends_only ? 'Yes' : 'No' }}</li>
                                        <li><strong>Created:</strong> {{ $confession->created_at->format('M j, Y H:i:s') }}</li>
                                        @if($confession->tags)
                                            <li><strong>Tags:</strong> 
                                                @foreach($confession->tags as $tag)
                                                    <span class="badge badge-secondary">{{ $tag }}</span>
                                                @endforeach
                                            </li>
                                        @endif
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>Engagement Stats</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>Views:</strong> {{ number_format($confession->views) }}</li>
                                        <li><strong>Likes:</strong> {{ number_format($confession->likes_count) }}</li>
                                        <li><strong>Bookmarks:</strong> {{ number_format($confession->bookmark_count) }}</li>
                                        <li><strong>Shares:</strong> {{ number_format($confession->share_count) }}</li>
                                        <li><strong>Engagement Score:</strong> {{ number_format($stats['engagement_score'], 2) }}</li>
                                    </ul>
                                </div>
                            </div>

                            @if($confession->ai_prompt)
                                <div class="mt-4">
                                    <h6>AI Generation Details</h6>
                                    <div class="bg-light p-3 rounded">
                                        <p><strong>Prompt:</strong> {{ $confession->ai_prompt }}</p>
                                        @if($confession->tone)
                                            <p><strong>Tone:</strong> {{ $confession->tone }}</p>
                                        @endif
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- User Information -->
                    @if(!$confession->is_anonymous && $confession->user)
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">User Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Name:</strong> {{ $confession->user->name }}</p>
                                    <p><strong>Email:</strong> {{ $confession->user->email }}</p>
                                    <p><strong>Joined:</strong> {{ $confession->user->created_at->format('M j, Y') }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Total Confessions:</strong> {{ $confession->user->confessions_count }}</p>
                                    <p><strong>Status:</strong> 
                                        @if($confession->user->is_banned)
                                            <span class="badge badge-danger">Banned</span>
                                        @elseif($confession->user->is_verified)
                                            <span class="badge badge-success">Verified</span>
                                        @else
                                            <span class="badge badge-secondary">Regular</span>
                                        @endif
                                    </p>
                                    <a href="{{ route('admin.users.show', $confession->user) }}" class="btn btn-sm btn-outline-primary">
                                        View User Profile
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Comments -->
                    @if($confession->comments->count() > 0)
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Comments ({{ $confession->comments->count() }})</h6>
                        </div>
                        <div class="card-body">
                            @foreach($confession->comments as $comment)
                                <div class="border-bottom pb-3 mb-3">
                                    <div class="d-flex justify-content-between">
                                        <strong>{{ $comment->user->name }}</strong>
                                        <small class="text-muted">{{ $comment->created_at->diffForHumans() }}</small>
                                    </div>
                                    <p class="mt-2 mb-0">{{ $comment->content }}</p>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Actions Sidebar -->
                <div class="col-lg-4">
                    <!-- Quick Actions -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                @if(!$confession->is_flagged)
                                    <form method="POST" action="{{ route('admin.moderation.confession.update', $confession) }}">
                                        @csrf
                                        @method('PATCH')
                                        <input type="hidden" name="action" value="flag">
                                        <button type="submit" class="btn btn-warning btn-block">
                                            <i class="fas fa-flag"></i> Flag Content
                                        </button>
                                    </form>
                                @else
                                    <form method="POST" action="{{ route('admin.moderation.confession.update', $confession) }}">
                                        @csrf
                                        @method('PATCH')
                                        <input type="hidden" name="action" value="approve">
                                        <button type="submit" class="btn btn-success btn-block">
                                            <i class="fas fa-check"></i> Approve Content
                                        </button>
                                    </form>
                                @endif

                                @if(!$confession->is_hidden)
                                    <form method="POST" action="{{ route('admin.moderation.confession.update', $confession) }}">
                                        @csrf
                                        @method('PATCH')
                                        <input type="hidden" name="action" value="hide">
                                        <button type="submit" class="btn btn-secondary btn-block">
                                            <i class="fas fa-eye-slash"></i> Hide Content
                                        </button>
                                    </form>
                                @endif

                                @if(!$confession->is_nsfw)
                                    <form method="POST" action="{{ route('admin.moderation.confession.update', $confession) }}">
                                        @csrf
                                        @method('PATCH')
                                        <input type="hidden" name="action" value="mark_nsfw">
                                        <button type="submit" class="btn btn-info btn-block">
                                            <i class="fas fa-eye-slash"></i> Mark as NSFW
                                        </button>
                                    </form>
                                @else
                                    <form method="POST" action="{{ route('admin.moderation.confession.update', $confession) }}">
                                        @csrf
                                        @method('PATCH')
                                        <input type="hidden" name="action" value="unmark_nsfw">
                                        <button type="submit" class="btn btn-outline-info btn-block">
                                            <i class="fas fa-eye"></i> Remove NSFW
                                        </button>
                                    </form>
                                @endif

                                <button type="button" class="btn btn-danger btn-block" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                    <i class="fas fa-trash"></i> Delete Content
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Reports -->
                    @if($confession->reports->count() > 0)
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Reports ({{ $stats['reports_count'] }})</h6>
                        </div>
                        <div class="card-body">
                            @foreach($confession->reports as $report)
                                <div class="border-bottom pb-2 mb-2">
                                    <div class="d-flex justify-content-between">
                                        <small><strong>{{ $report->user->name }}</strong></small>
                                        <small class="text-muted">{{ $report->created_at->diffForHumans() }}</small>
                                    </div>
                                    <p class="mb-1"><strong>Type:</strong> {{ ucfirst($report->type) }}</p>
                                    @if($report->reason)
                                        <p class="mb-1 text-muted">{{ $report->reason }}</p>
                                    @endif
                                    <span class="badge badge-{{ $report->status === 'pending' ? 'warning' : ($report->status === 'resolved' ? 'success' : 'secondary') }}">
                                        {{ ucfirst($report->status) }}
                                    </span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    @endif

                    <!-- Statistics -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Statistics</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-right">
                                        <h4 class="text-primary">{{ $stats['reports_count'] }}</h4>
                                        <small>Total Reports</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-warning">{{ $stats['pending_reports'] }}</h4>
                                    <small>Pending Reports</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Deletion</h5>
                <button type="button" class="close" data-bs-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ route('admin.moderation.confession.update', $confession) }}">
                @csrf
                @method('PATCH')
                <input type="hidden" name="action" value="delete">
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <strong>Warning!</strong> This action cannot be undone. The confession will be permanently deleted.
                    </div>
                    <p>Are you sure you want to delete this confession?</p>
                    <div class="form-group">
                        <label for="deleteReason">Reason for deletion (required)</label>
                        <textarea name="reason" id="deleteReason" class="form-control" rows="3" placeholder="Provide a detailed reason for deletion..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Confession
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
