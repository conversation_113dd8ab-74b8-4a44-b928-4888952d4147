@extends('admin.layout')

@section('title', 'AI Settings')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-white">AI Settings</h1>
            <p class="text-gray-400">Configure AI models, API settings, and generation parameters</p>
        </div>
        <div class="flex space-x-3">
            <button onclick="testConnection()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                Test Connection
            </button>
            <button onclick="resetDefaults()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                Reset Defaults
            </button>
        </div>
    </div>

    <form method="POST" action="{{ route('admin.ai.settings.update') }}" class="space-y-6">
        @csrf

        <!-- API Configuration -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-xl font-semibold text-white mb-4">API Configuration</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-gray-400 text-sm mb-2">API Provider</label>
                    <select name="api_provider" class="w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500">
                        <option value="groq" {{ ($settings['api_provider'] ?? 'groq') == 'groq' ? 'selected' : '' }}>Groq</option>
                        <option value="openai" {{ ($settings['api_provider'] ?? '') == 'openai' ? 'selected' : '' }}>OpenAI</option>
                        <option value="anthropic" {{ ($settings['api_provider'] ?? '') == 'anthropic' ? 'selected' : '' }}>Anthropic</option>
                        <option value="cohere" {{ ($settings['api_provider'] ?? '') == 'cohere' ? 'selected' : '' }}>Cohere</option>
                    </select>
                </div>

                <div>
                    <label class="block text-gray-400 text-sm mb-2">API Key</label>
                    <input type="password" name="api_key" value="{{ $settings['api_key'] ?? '' }}" class="w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500" placeholder="Enter API key">
                </div>

                <div>
                    <label class="block text-gray-400 text-sm mb-2">API Endpoint</label>
                    <input type="url" name="api_endpoint" value="{{ $settings['api_endpoint'] ?? 'https://api.groq.com/openai/v1/chat/completions' }}" class="w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500">
                </div>

                <div>
                    <label class="block text-gray-400 text-sm mb-2">Request Timeout (seconds)</label>
                    <input type="number" name="request_timeout" value="{{ $settings['request_timeout'] ?? 30 }}" min="5" max="120" class="w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500">
                </div>
            </div>
        </div>

        <!-- Model Configuration -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-xl font-semibold text-white mb-4">Model Configuration</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-gray-400 text-sm mb-2">Default Model</label>
                    <select name="default_model" class="w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500">
                        <option value="llama-3.1-70b-versatile" {{ ($settings['default_model'] ?? 'llama-3.1-70b-versatile') == 'llama-3.1-70b-versatile' ? 'selected' : '' }}>Llama 3.1 70B Versatile</option>
                        <option value="llama-3.1-8b-instant" {{ ($settings['default_model'] ?? '') == 'llama-3.1-8b-instant' ? 'selected' : '' }}>Llama 3.1 8B Instant</option>
                        <option value="mixtral-8x7b-32768" {{ ($settings['default_model'] ?? '') == 'mixtral-8x7b-32768' ? 'selected' : '' }}>Mixtral 8x7B</option>
                        <option value="gemma2-9b-it" {{ ($settings['default_model'] ?? '') == 'gemma2-9b-it' ? 'selected' : '' }}>Gemma2 9B IT</option>
                    </select>
                </div>

                <div>
                    <label class="block text-gray-400 text-sm mb-2">Fallback Model</label>
                    <select name="fallback_model" class="w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500">
                        <option value="llama-3.1-8b-instant" {{ ($settings['fallback_model'] ?? 'llama-3.1-8b-instant') == 'llama-3.1-8b-instant' ? 'selected' : '' }}>Llama 3.1 8B Instant</option>
                        <option value="llama-3.1-70b-versatile" {{ ($settings['fallback_model'] ?? '') == 'llama-3.1-70b-versatile' ? 'selected' : '' }}>Llama 3.1 70B Versatile</option>
                        <option value="mixtral-8x7b-32768" {{ ($settings['fallback_model'] ?? '') == 'mixtral-8x7b-32768' ? 'selected' : '' }}>Mixtral 8x7B</option>
                    </select>
                </div>

                <div>
                    <label class="block text-gray-400 text-sm mb-2">Temperature</label>
                    <input type="range" name="temperature" value="{{ $settings['temperature'] ?? 0.7 }}" min="0" max="2" step="0.1" class="w-full" oninput="updateTemperatureValue(this.value)">
                    <div class="flex justify-between text-gray-400 text-sm mt-1">
                        <span>Conservative (0)</span>
                        <span id="temperatureValue">{{ $settings['temperature'] ?? 0.7 }}</span>
                        <span>Creative (2)</span>
                    </div>
                </div>

                <div>
                    <label class="block text-gray-400 text-sm mb-2">Max Tokens</label>
                    <input type="number" name="max_tokens" value="{{ $settings['max_tokens'] ?? 1000 }}" min="100" max="4000" class="w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500">
                </div>
            </div>
        </div>

        <!-- Generation Settings -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-xl font-semibold text-white mb-4">Generation Settings</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-gray-400 text-sm mb-2">Rate Limit (requests/minute)</label>
                    <input type="number" name="rate_limit" value="{{ $settings['rate_limit'] ?? 60 }}" min="1" max="1000" class="w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500">
                </div>

                <div>
                    <label class="block text-gray-400 text-sm mb-2">Daily Usage Limit</label>
                    <input type="number" name="daily_limit" value="{{ $settings['daily_limit'] ?? 10000 }}" min="100" class="w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500">
                </div>

                <div>
                    <label class="block text-gray-400 text-sm mb-2">Cost Alert Threshold ($)</label>
                    <input type="number" name="cost_threshold" value="{{ $settings['cost_threshold'] ?? 100 }}" min="1" step="0.01" class="w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500">
                </div>

                <div>
                    <label class="block text-gray-400 text-sm mb-2">Retry Attempts</label>
                    <input type="number" name="retry_attempts" value="{{ $settings['retry_attempts'] ?? 3 }}" min="1" max="10" class="w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500">
                </div>
            </div>
        </div>

        <!-- Content Filtering -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-xl font-semibold text-white mb-4">Content Filtering</h2>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div>
                        <label class="text-white font-medium">Enable Content Filtering</label>
                        <p class="text-gray-400 text-sm">Filter inappropriate content before generation</p>
                    </div>
                    <input type="checkbox" name="content_filtering" value="1" {{ ($settings['content_filtering'] ?? true) ? 'checked' : '' }} class="toggle">
                </div>

                <div class="flex items-center justify-between">
                    <div>
                        <label class="text-white font-medium">NSFW Content Detection</label>
                        <p class="text-gray-400 text-sm">Detect and flag NSFW content</p>
                    </div>
                    <input type="checkbox" name="nsfw_detection" value="1" {{ ($settings['nsfw_detection'] ?? true) ? 'checked' : '' }} class="toggle">
                </div>

                <div class="flex items-center justify-between">
                    <div>
                        <label class="text-white font-medium">Spam Detection</label>
                        <p class="text-gray-400 text-sm">Detect repetitive or spam content</p>
                    </div>
                    <input type="checkbox" name="spam_detection" value="1" {{ ($settings['spam_detection'] ?? true) ? 'checked' : '' }} class="toggle">
                </div>

                <div class="flex items-center justify-between">
                    <div>
                        <label class="text-white font-medium">Quality Threshold</label>
                        <p class="text-gray-400 text-sm">Minimum quality score for generated content</p>
                    </div>
                    <input type="range" name="quality_threshold" value="{{ $settings['quality_threshold'] ?? 0.7 }}" min="0" max="1" step="0.1" class="w-32" oninput="updateQualityValue(this.value)">
                    <span id="qualityValue" class="text-white ml-2">{{ $settings['quality_threshold'] ?? 0.7 }}</span>
                </div>
            </div>
        </div>

        <!-- Monitoring & Logging -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h2 class="text-xl font-semibold text-white mb-4">Monitoring & Logging</h2>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div>
                        <label class="text-white font-medium">Enable Usage Logging</label>
                        <p class="text-gray-400 text-sm">Log all AI API requests for analytics</p>
                    </div>
                    <input type="checkbox" name="usage_logging" value="1" {{ ($settings['usage_logging'] ?? true) ? 'checked' : '' }} class="toggle">
                </div>

                <div class="flex items-center justify-between">
                    <div>
                        <label class="text-white font-medium">Performance Monitoring</label>
                        <p class="text-gray-400 text-sm">Monitor response times and success rates</p>
                    </div>
                    <input type="checkbox" name="performance_monitoring" value="1" {{ ($settings['performance_monitoring'] ?? true) ? 'checked' : '' }} class="toggle">
                </div>

                <div class="flex items-center justify-between">
                    <div>
                        <label class="text-white font-medium">Error Notifications</label>
                        <p class="text-gray-400 text-sm">Send notifications for API errors</p>
                    </div>
                    <input type="checkbox" name="error_notifications" value="1" {{ ($settings['error_notifications'] ?? true) ? 'checked' : '' }} class="toggle">
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="flex justify-end">
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                Save Settings
            </button>
        </div>
    </form>
</div>

<script>
function updateTemperatureValue(value) {
    document.getElementById('temperatureValue').textContent = value;
}

function updateQualityValue(value) {
    document.getElementById('qualityValue').textContent = value;
}

function testConnection() {
    // Implementation for testing API connection
    alert('Testing API connection...');
}

function resetDefaults() {
    if (confirm('Are you sure you want to reset all settings to defaults?')) {
        // Implementation for resetting to defaults
        location.reload();
    }
}
</script>
@endsection
