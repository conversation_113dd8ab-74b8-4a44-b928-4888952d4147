@extends('admin.layout')

@section('title', 'AI Prompt Templates')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-white">AI Prompt Templates</h1>
            <p class="text-gray-400">Manage and optimize AI prompt templates for content generation</p>
        </div>
        <button onclick="openCreateModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
            Create Template
        </button>
    </div>

    <!-- Templates Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        @forelse($templates as $template)
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex justify-between items-start mb-4">
                <div>
                    <h3 class="text-lg font-semibold text-white">{{ $template->name }}</h3>
                    <p class="text-gray-400 text-sm">{{ $template->description }}</p>
                </div>
                <div class="flex space-x-2">
                    <button onclick="editTemplate({{ $template->id }})" class="text-blue-400 hover:text-blue-300">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                        </svg>
                    </button>
                    <button onclick="deleteTemplate({{ $template->id }})" class="text-red-400 hover:text-red-300">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9zM4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 102 0v-1a1 1 0 10-2 0v1zm4 0a1 1 0 102 0v-1a1 1 0 10-2 0v1z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="space-y-3">
                <div>
                    <label class="text-gray-400 text-sm">Category</label>
                    <p class="text-white">{{ ucfirst($template->category) }}</p>
                </div>

                <div>
                    <label class="text-gray-400 text-sm">Template Content</label>
                    <div class="bg-gray-700 rounded p-3 mt-1">
                        <code class="text-green-400 text-sm">{{ Str::limit($template->template, 200) }}</code>
                    </div>
                </div>

                @if($template->variables)
                <div>
                    <label class="text-gray-400 text-sm">Variables</label>
                    <div class="flex flex-wrap gap-2 mt-1">
                        @foreach($template->variables as $variable)
                        <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">{{ $variable }}</span>
                        @endforeach
                    </div>
                </div>
                @endif

                <div class="flex justify-between items-center pt-3 border-t border-gray-700">
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-400 text-sm">Uses: {{ $template->usage_count }}</span>
                        <span class="text-gray-400 text-sm">Success: {{ number_format($template->success_rate, 1) }}%</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 rounded text-xs {{ $template->is_active ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400' }}">
                            {{ $template->is_active ? 'Active' : 'Inactive' }}
                        </span>
                        <button onclick="testTemplate({{ $template->id }})" class="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-xs transition-colors">
                            Test
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @empty
        <div class="col-span-2 text-center py-12">
            <svg class="w-12 h-12 text-gray-600 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z" clip-rule="evenodd"/>
            </svg>
            <h3 class="text-lg font-semibold text-white mb-2">No Templates Found</h3>
            <p class="text-gray-400 mb-4">Create your first AI prompt template to get started</p>
            <button onclick="openCreateModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                Create Template
            </button>
        </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($templates->hasPages())
    <div class="flex justify-center">
        {{ $templates->links() }}
    </div>
    @endif
</div>

<!-- Create/Edit Modal -->
<div id="templateModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-gray-800 rounded-lg p-6 w-full max-w-2xl">
            <div class="flex justify-between items-center mb-6">
                <h2 id="modalTitle" class="text-xl font-bold text-white">Create Template</h2>
                <button onclick="closeModal()" class="text-gray-400 hover:text-white">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                </button>
            </div>

            <form id="templateForm" method="POST" action="{{ route('admin.ai.templates.store') }}">
                @csrf
                <div class="space-y-4">
                    <div>
                        <label class="block text-gray-400 text-sm mb-2">Name</label>
                        <input type="text" name="name" required class="w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500">
                    </div>

                    <div>
                        <label class="block text-gray-400 text-sm mb-2">Description</label>
                        <input type="text" name="description" class="w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500">
                    </div>

                    <div>
                        <label class="block text-gray-400 text-sm mb-2">Category</label>
                        <select name="category" required class="w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500">
                            <option value="">Select Category</option>
                            <option value="confession">Confession</option>
                            <option value="story">Story</option>
                            <option value="creative">Creative</option>
                            <option value="nsfw">NSFW</option>
                            <option value="general">General</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-gray-400 text-sm mb-2">Template Content</label>
                        <textarea name="template" rows="8" required class="w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500" placeholder="Enter your prompt template here. Use {variable_name} for dynamic content."></textarea>
                    </div>

                    <div>
                        <label class="block text-gray-400 text-sm mb-2">Variables (comma-separated)</label>
                        <input type="text" name="variables" class="w-full bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-2 focus:outline-none focus:border-blue-500" placeholder="topic, tone, length">
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" name="is_active" value="1" checked class="mr-2">
                        <label class="text-gray-400 text-sm">Active</label>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeModal()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                        Cancel
                    </button>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                        Save Template
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openCreateModal() {
    document.getElementById('modalTitle').textContent = 'Create Template';
    document.getElementById('templateForm').action = '{{ route("admin.ai.templates.store") }}';
    document.getElementById('templateForm').reset();
    document.getElementById('templateModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('templateModal').classList.add('hidden');
}

function editTemplate(id) {
    // Implementation for editing template
    console.log('Edit template:', id);
}

function deleteTemplate(id) {
    if (confirm('Are you sure you want to delete this template?')) {
        // Implementation for deleting template
        console.log('Delete template:', id);
    }
}

function testTemplate(id) {
    // Implementation for testing template
    console.log('Test template:', id);
}
</script>
@endsection
