@extends('admin.layout')

@section('title', 'AI Management')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">AI Management</h1>
                <p class="text-gray-600">Monitor AI usage, costs, and performance</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('admin.ai.templates') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Manage Templates
                </a>
                <a href="{{ route('admin.ai.settings') }}" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    Settings
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Requests Today</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['total_requests_today']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Cost Today</p>
                    <p class="text-2xl font-semibold text-gray-900">${{ number_format($stats['total_cost_today'], 2) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Success Rate</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['success_rate_today'], 1) }}%</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Avg Response</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['avg_response_time']) }}ms</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Daily Usage Chart -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Daily Usage (7 Days)</h3>
            <canvas id="dailyUsageChart" width="400" height="200"></canvas>
        </div>

        <!-- Model Usage -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Usage by Model (Today)</h3>
            <div class="space-y-4">
                @forelse($modelUsage as $model)
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-medium text-gray-900">{{ $model->model_name }}</p>
                            <p class="text-sm text-gray-600">{{ number_format($model->requests) }} requests</p>
                        </div>
                        <div class="text-right">
                            <p class="font-medium text-gray-900">${{ number_format($model->cost, 2) }}</p>
                            <p class="text-sm text-gray-600">{{ number_format($model->tokens) }} tokens</p>
                        </div>
                    </div>
                @empty
                    <p class="text-gray-500 text-center py-4">No usage data for today</p>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Usage by Type & Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Usage by Type -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Usage by Type (Today)</h3>
            <div class="space-y-4">
                @forelse($typeUsage as $type)
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-medium text-gray-900 capitalize">{{ str_replace('_', ' ', $type->request_type) }}</p>
                            <p class="text-sm text-gray-600">{{ number_format($type->requests) }} requests</p>
                        </div>
                        <div class="text-right">
                            <p class="font-medium text-gray-900">${{ number_format($type->cost, 2) }}</p>
                            <p class="text-sm text-gray-600">{{ number_format($type->tokens) }} tokens</p>
                        </div>
                    </div>
                @empty
                    <p class="text-gray-500 text-center py-4">No usage data for today</p>
                @endforelse
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
            <div class="space-y-3">
                @forelse($recentActivity as $activity)
                    <div class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                        <div>
                            <p class="text-sm font-medium text-gray-900">
                                {{ $activity->user ? $activity->user->name : 'System' }}
                            </p>
                            <p class="text-xs text-gray-600">
                                {{ $activity->model_name }} • {{ $activity->request_type }}
                            </p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium {{ $activity->success ? 'text-green-600' : 'text-red-600' }}">
                                {{ $activity->success ? 'Success' : 'Failed' }}
                            </p>
                            <p class="text-xs text-gray-600">{{ $activity->created_at->diffForHumans() }}</p>
                        </div>
                    </div>
                @empty
                    <p class="text-gray-500 text-center py-4">No recent activity</p>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <a href="{{ route('admin.ai.usage') }}" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="text-center">
                    <svg class="w-8 h-8 text-blue-600 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                    </svg>
                    <p class="font-medium text-gray-900">View Usage</p>
                    <p class="text-sm text-gray-600">Detailed usage logs</p>
                </div>
            </a>

            <a href="{{ route('admin.ai.costs') }}" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="text-center">
                    <svg class="w-8 h-8 text-green-600 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                    </svg>
                    <p class="font-medium text-gray-900">Cost Analysis</p>
                    <p class="text-sm text-gray-600">Track spending</p>
                </div>
            </a>

            <a href="{{ route('admin.ai.analytics') }}" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="text-center">
                    <svg class="w-8 h-8 text-purple-600 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"/>
                        <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"/>
                    </svg>
                    <p class="font-medium text-gray-900">Analytics</p>
                    <p class="text-sm text-gray-600">Performance insights</p>
                </div>
            </a>

            <a href="{{ route('admin.ai.templates.create') }}" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="text-center">
                    <svg class="w-8 h-8 text-yellow-600 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                    </svg>
                    <p class="font-medium text-gray-900">New Template</p>
                    <p class="text-sm text-gray-600">Create prompt</p>
                </div>
            </a>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Daily Usage Chart
const dailyUsageCtx = document.getElementById('dailyUsageChart').getContext('2d');
const dailyUsageChart = new Chart(dailyUsageCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode($dailyUsage->pluck('date')) !!},
        datasets: [{
            label: 'Requests',
            data: {!! json_encode($dailyUsage->pluck('requests')) !!},
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.1
        }, {
            label: 'Cost ($)',
            data: {!! json_encode($dailyUsage->pluck('cost')) !!},
            borderColor: 'rgb(16, 185, 129)',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.1,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                grid: {
                    drawOnChartArea: false,
                },
            }
        }
    }
});
</script>
@endsection
