@extends('admin.layout')

@section('title', 'Admin Dashboard')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
                <p class="text-gray-600">Welcome back! Here's what's happening with your confession platform.</p>
            </div>
            <div class="flex space-x-3">
                <button onclick="refreshDashboard()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    🔄 Refresh
                </button>
                <button onclick="checkSystemHealth()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    ❤️ System Health
                </button>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Users -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="p-3 bg-blue-100 rounded-full">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Users</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_users']) }}</p>
                    <p class="text-sm text-green-600">
                        +{{ $stats['new_users_today'] }} today
                    </p>
                </div>
            </div>
        </div>

        <!-- Confessions -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="p-3 bg-purple-100 rounded-full">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Confessions</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_confessions']) }}</p>
                    <p class="text-sm text-green-600">
                        +{{ $stats['confessions_today'] }} today
                    </p>
                </div>
            </div>
        </div>

        <!-- Reports -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="p-3 bg-red-100 rounded-full">
                    <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Pending Reports</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['pending_reports']) }}</p>
                    @if($stats['high_priority_reports'] > 0)
                        <p class="text-sm text-red-600">
                            {{ $stats['high_priority_reports'] }} high priority
                        </p>
                    @else
                        <p class="text-sm text-green-600">No high priority</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Active Users -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="p-3 bg-green-100 rounded-full">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active This Week</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['active_users_week']) }}</p>
                    <p class="text-sm text-gray-600">
                        {{ round(($stats['active_users_week'] / max($stats['total_users'], 1)) * 100, 1) }}% of total
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Activity Chart -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Platform Activity (Last 7 Days)</h3>
            <canvas id="activityChart" width="400" height="200"></canvas>
        </div>

        <!-- Category Distribution -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Popular Categories</h3>
            <div class="space-y-3">
                @foreach($chartData['categories']->take(8) as $category)
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-700 capitalize">{{ $category->category }}</span>
                        <div class="flex items-center space-x-2">
                            <div class="w-24 bg-gray-200 rounded-full h-2">
                                <div class="bg-purple-600 h-2 rounded-full" 
                                     style="width: {{ ($category->count / $chartData['categories']->first()->count) * 100 }}%"></div>
                            </div>
                            <span class="text-sm text-gray-600">{{ $category->count }}</span>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Recent Activity & Pending Reports -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
            <div class="space-y-4 max-h-96 overflow-y-auto">
                @foreach($recentActivity as $activity)
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 w-8 h-8 bg-{{ $activity['color'] }}-100 rounded-full flex items-center justify-center">
                            <span class="text-sm">{{ $activity['icon'] }}</span>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm text-gray-900">{{ $activity['message'] }}</p>
                            <p class="text-xs text-gray-500">{{ $activity['time']->diffForHumans() }}</p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Pending Reports -->
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Pending Reports</h3>
                <a href="{{ route('admin.moderation.reports') }}" class="text-sm text-blue-600 hover:text-blue-800">
                    View All →
                </a>
            </div>
            <div class="space-y-4 max-h-96 overflow-y-auto">
                @forelse($pendingReports as $report)
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-900">{{ $report->reason }}</span>
                            <span class="px-2 py-1 text-xs rounded-full 
                                {{ $report->severity === 'high' || $report->severity === 'critical' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800' }}">
                                {{ ucfirst($report->severity ?? 'medium') }}
                            </span>
                        </div>
                        <p class="text-sm text-gray-600 mb-2">
                            Reported by {{ $report->user->name }} • {{ $report->created_at->diffForHumans() }}
                        </p>
                        <div class="flex space-x-2">
                            <a href="{{ route('admin.moderation.report', $report) }}" 
                               class="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">
                                Review
                            </a>
                        </div>
                    </div>
                @empty
                    <p class="text-gray-500 text-center py-8">No pending reports! 🎉</p>
                @endforelse
            </div>
        </div>
    </div>

    <!-- System Stats -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">System Statistics</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-blue-600">{{ number_format($stats['ai_confessions']) }}</p>
                <p class="text-sm text-gray-600">AI Confessions</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-red-600">{{ number_format($stats['nsfw_confessions']) }}</p>
                <p class="text-sm text-gray-600">NSFW Content</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-green-600">{{ number_format($stats['verified_users']) }}</p>
                <p class="text-sm text-gray-600">Verified Users</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-yellow-600">{{ number_format($stats['banned_users']) }}</p>
                <p class="text-sm text-gray-600">Banned Users</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-purple-600">{{ number_format($stats['total_messages']) }}</p>
                <p class="text-sm text-gray-600">Messages Sent</p>
            </div>
            <div class="text-center">
                <p class="text-2xl font-bold text-indigo-600">{{ number_format($stats['active_chat_rooms']) }}</p>
                <p class="text-sm text-gray-600">Active Rooms</p>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Activity Chart
const ctx = document.getElementById('activityChart').getContext('2d');
const activityChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: {!! json_encode($chartData['user_registrations']->pluck('date')) !!},
        datasets: [{
            label: 'New Users',
            data: {!! json_encode($chartData['user_registrations']->pluck('count')) !!},
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4
        }, {
            label: 'New Confessions',
            data: {!! json_encode($chartData['confession_posts']->pluck('count')) !!},
            borderColor: 'rgb(147, 51, 234)',
            backgroundColor: 'rgba(147, 51, 234, 0.1)',
            tension: 0.4
        }, {
            label: 'Reports',
            data: {!! json_encode($chartData['report_submissions']->pluck('count')) !!},
            borderColor: 'rgb(239, 68, 68)',
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

function refreshDashboard() {
    location.reload();
}

async function checkSystemHealth() {
    try {
        const response = await fetch('{{ route("admin.system.health") }}');
        const health = await response.json();
        
        let message = 'System Health Status:\n\n';
        Object.entries(health).forEach(([system, status]) => {
            message += `${system.toUpperCase()}: ${status.status}\n`;
        });
        
        alert(message);
    } catch (error) {
        alert('Error checking system health');
    }
}
</script>
@endsection
