@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto">
    <!-- Profile Header -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex items-center space-x-6">
            <div class="w-20 h-20 bg-gray-300 rounded-full flex items-center justify-center">
                @if($user->avatar)
                    <img src="{{ $user->avatar }}" alt="{{ $user->name }}" class="w-20 h-20 rounded-full">
                @else
                    <span class="text-2xl font-bold text-gray-600">{{ substr($user->name, 0, 1) }}</span>
                @endif
            </div>
            
            <div class="flex-1">
                <h1 class="text-2xl font-bold text-gray-900">{{ $user->name }}</h1>
                <p class="text-gray-600">@{{ $user->username }}</p>
                
                <div class="flex items-center space-x-6 mt-4">
                    <div class="text-center">
                        <div class="text-xl font-bold text-blue-600">{{ $stats['public_confessions'] }}</div>
                        <div class="text-sm text-gray-500">Confessions</div>
                    </div>
                    <div class="text-center">
                        <div class="text-xl font-bold text-red-600">{{ $stats['total_likes'] }}</div>
                        <div class="text-sm text-gray-500">Likes</div>
                    </div>
                    <div class="text-center">
                        <div class="text-xl font-bold text-green-600">{{ $stats['followers_count'] }}</div>
                        <div class="text-sm text-gray-500">Followers</div>
                    </div>
                    <div class="text-center">
                        <div class="text-xl font-bold text-purple-600">{{ $stats['following_count'] }}</div>
                        <div class="text-sm text-gray-500">Following</div>
                    </div>
                </div>
            </div>
            
            @auth
                @if(auth()->id() !== $user->id)
                    <button onclick="toggleFollow({{ $user->id }})" 
                            class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        Follow
                    </button>
                @endif
            @endauth
        </div>
        
        <!-- User Badges -->
        @if($user->badges->count() > 0)
            <div class="mt-6">
                <h3 class="text-lg font-semibold mb-3">Badges</h3>
                <div class="flex flex-wrap gap-2">
                    @foreach($user->badges as $badge)
                        <div class="flex items-center px-3 py-1 {{ $badge->color }} bg-opacity-20 rounded-full">
                            <span class="mr-1">{{ $badge->icon }}</span>
                            <span class="text-sm font-medium">{{ $badge->name }}</span>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    </div>
    
    <!-- User's Confessions -->
    <div class="space-y-6">
        <h2 class="text-2xl font-bold text-gray-900">Public Confessions</h2>
        
        @forelse($confessions as $confession)
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start mb-4">
                    <div class="flex items-center space-x-2">
                        @if($confession->category)
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                                {{ $confession->category }}
                            </span>
                        @endif
                    </div>
                    <span class="text-gray-500 text-sm">{{ $confession->created_at->diffForHumans() }}</span>
                </div>

                <p class="text-gray-800 mb-4">{{ $confession->content }}</p>
                
                @if($confession->image)
                    <div class="mb-4">
                        <img src="{{ asset('storage/' . $confession->image) }}" 
                             alt="Confession image" 
                             class="max-w-full h-auto rounded-lg">
                    </div>
                @endif

                <div class="flex items-center space-x-4 text-gray-500">
                    <div class="flex items-center space-x-1">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                        </svg>
                        <span>{{ $confession->likes_count }}</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        <span>{{ $confession->comments->count() }}</span>
                    </div>
                    <a href="{{ route('confessions.show', $confession) }}" class="text-blue-500 hover:text-blue-700">
                        Read More
                    </a>
                </div>
            </div>
        @empty
            <div class="text-center py-12">
                <p class="text-gray-500">No public confessions yet.</p>
            </div>
        @endforelse
        
        {{ $confessions->links() }}
    </div>
</div>

<script>
function toggleFollow(userId) {
    fetch(`/users/${userId}/follow`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Accept': 'application/json'
        }
    }).then(() => location.reload());
}
</script>
@endsection