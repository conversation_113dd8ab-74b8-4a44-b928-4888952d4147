@extends('layouts.app')

@section('content')
<div class="max-w-2xl mx-auto">
    <h1 class="text-3xl font-bold text-gray-900 mb-6">Create Voice Room</h1>

    <div class="bg-white rounded-lg shadow-md p-6">
        <form method="POST" action="{{ route('voice-rooms.store') }}">
            @csrf
            
            <div class="mb-6">
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                    Room Name
                </label>
                <input type="text" name="name" id="name" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                       placeholder="Enter room name..." value="{{ old('name') }}" required>
                @error('name')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description (Optional)
                </label>
                <textarea name="description" id="description" rows="3" 
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          placeholder="What will you talk about?">{{ old('description') }}</textarea>
                @error('description')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                    Category (Optional)
                </label>
                <select name="category" id="category" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    <option value="">Select a category</option>
                    <option value="General Chat" {{ old('category') == 'General Chat' ? 'selected' : '' }}>General Chat</option>
                    <option value="Confessions" {{ old('category') == 'Confessions' ? 'selected' : '' }}>Confessions</option>
                    <option value="Support" {{ old('category') == 'Support' ? 'selected' : '' }}>Support</option>
                    <option value="Gaming" {{ old('category') == 'Gaming' ? 'selected' : '' }}>Gaming</option>
                    <option value="Music" {{ old('category') == 'Music' ? 'selected' : '' }}>Music</option>
                    <option value="Study" {{ old('category') == 'Study' ? 'selected' : '' }}>Study</option>
                    <option value="Other" {{ old('category') == 'Other' ? 'selected' : '' }}>Other</option>
                </select>
                @error('category')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label for="max_participants" class="block text-sm font-medium text-gray-700 mb-2">
                    Maximum Participants
                </label>
                <select name="max_participants" id="max_participants" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    <option value="5" {{ old('max_participants') == '5' ? 'selected' : '' }}>5 people</option>
                    <option value="10" {{ old('max_participants', '10') == '10' ? 'selected' : '' }}>10 people</option>
                    <option value="15" {{ old('max_participants') == '15' ? 'selected' : '' }}>15 people</option>
                    <option value="20" {{ old('max_participants') == '20' ? 'selected' : '' }}>20 people</option>
                    <option value="30" {{ old('max_participants') == '30' ? 'selected' : '' }}>30 people</option>
                </select>
                @error('max_participants')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label class="flex items-center">
                    <input type="checkbox" name="is_private" value="1" 
                           class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50"
                           {{ old('is_private') ? 'checked' : '' }}>
                    <span class="ml-2 text-sm text-gray-700">Make this room private</span>
                </label>
                <p class="text-xs text-gray-500 mt-1">
                    Private rooms won't appear in the public room list
                </p>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
                <div class="flex">
                    <svg class="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <h3 class="text-sm font-medium text-yellow-800">Voice Room Guidelines</h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <ul class="list-disc list-inside space-y-1">
                                <li>Be respectful to all participants</li>
                                <li>No inappropriate content or harassment</li>
                                <li>As the host, you can mute or remove participants</li>
                                <li>Voice rooms are for audio only - no video</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex items-center justify-between">
                <a href="{{ route('voice-rooms.index') }}" 
                   class="text-gray-600 hover:text-gray-800">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                    Create Room
                </button>
            </div>
        </form>
    </div>
</div>
@endsection