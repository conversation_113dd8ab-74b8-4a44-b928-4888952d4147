@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto">
    <!-- Room Header -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex justify-between items-start mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ $voiceRoom->name }}</h1>
                @if($voiceRoom->description)
                    <p class="text-gray-600 mb-4">{{ $voiceRoom->description }}</p>
                @endif

                <div class="flex items-center space-x-4 text-sm text-gray-500">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                        </svg>
                        Host: {{ $voiceRoom->host->name }}
                        @if($voiceRoom->host->is_verified)
                            <svg class="w-4 h-4 ml-1 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        @endif
                    </div>

                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                        </svg>
                        <span id="participantCount">{{ $voiceRoom->current_participants }}</span>/{{ $voiceRoom->max_participants }} participants
                    </div>

                    @if($voiceRoom->category)
                        <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded">
                            {{ $voiceRoom->category }}
                        </span>
                    @endif

                    <span class="flex items-center text-green-600">
                        <span class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
                        Live
                    </span>
                </div>
            </div>
            
            @auth
                <div class="flex items-center space-x-2">
                    @if($voiceRoom->host_id === auth()->id())
                        <a href="{{ route('voice-rooms.edit', $voiceRoom) }}"
                           class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                            Edit Room
                        </a>
                        <form method="POST" action="{{ route('voice-rooms.destroy', $voiceRoom) }}"
                              class="inline" onsubmit="return confirm('Are you sure you want to end this room?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600">
                                End Room
                            </button>
                        </form>
                    @else
                        @if($currentParticipant)
                            <button id="leaveRoomBtn" onclick="leaveRoom()"
                                    class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600">
                                Leave Room
                            </button>
                        @else
                            <button id="joinRoomBtn" onclick="joinRoom()"
                                    class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600">
                                Join Room
                            </button>
                        @endif
                    @endif

                    <!-- Invite Friends Button -->
                    <button id="inviteFriendsBtn" onclick="showInviteModal()"
                            class="bg-purple-500 text-white px-4 py-2 rounded-lg hover:bg-purple-600">
                        Invite Friends
                    </button>
                </div>
            @endauth
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Voice Chat Interface -->
        <div class="lg:col-span-2">
            <div class="bg-gray-900 rounded-lg p-6">
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-bold text-white mb-2">Voice Chat</h2>
                    <p class="text-gray-300">Click the microphone to start talking</p>
                </div>

                @auth
                    @if($currentParticipant)
                        <!-- Microphone Controls -->
                        <div class="flex justify-center items-center space-x-4 mb-6">
                            <button id="micButton" onclick="toggleMicrophone()"
                                    class="w-16 h-16 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center transition-colors">
                                <svg id="micIcon" class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"></path>
                                    </svg>
                            </button>
                        </div>

                        <!-- Audio Status -->
                        <div class="text-center">
                            <p id="audioStatus" class="text-gray-300">
                                @if($currentParticipant->is_muted_by_host)
                                    Muted by host
                                @elseif($currentParticipant->is_self_muted)
                                    Microphone is muted
                                @else
                                    Microphone is active
                                @endif
                            </p>

                            <!-- Audio Visualization -->
                            <div id="audioVisualization" class="mt-3 hidden">
                                <div class="flex justify-center items-end space-x-1 h-8">
                                    <div class="w-1 bg-green-500 rounded-full audio-bar" style="height: 10%"></div>
                                    <div class="w-1 bg-green-500 rounded-full audio-bar" style="height: 20%"></div>
                                    <div class="w-1 bg-green-500 rounded-full audio-bar" style="height: 30%"></div>
                                    <div class="w-1 bg-green-500 rounded-full audio-bar" style="height: 40%"></div>
                                    <div class="w-1 bg-green-500 rounded-full audio-bar" style="height: 50%"></div>
                                    <div class="w-1 bg-green-500 rounded-full audio-bar" style="height: 40%"></div>
                                    <div class="w-1 bg-green-500 rounded-full audio-bar" style="height: 30%"></div>
                                    <div class="w-1 bg-green-500 rounded-full audio-bar" style="height: 20%"></div>
                                    <div class="w-1 bg-green-500 rounded-full audio-bar" style="height: 10%"></div>
                                </div>
                                <p class="text-xs text-gray-400 mt-1">Audio level indicator</p>
                            </div>

                            <!-- Voice Note -->
                            <div id="voiceNote" class="mt-3 text-sm text-green-600 bg-green-50 p-3 rounded-lg">
                                <strong>🎉 WebRTC Voice Chat Active!</strong><br>
                                Full voice chat functionality is now enabled with WebRTC peer-to-peer connections.
                                You can now hear other participants and they can hear you when you unmute your microphone.
                                <div class="mt-2 text-xs text-gray-600">
                                    <strong>Features:</strong> Real-time voice communication, audio visualization, automatic connection management
                                </div>
                            </div>

                            <!-- Connected Participants -->
                            <div id="connectedParticipants" class="mt-3 text-sm">
                                <div class="text-gray-600 font-medium mb-2">🔊 Voice Connections:</div>
                                <div id="participantsList" class="space-y-1 text-xs">
                                    <div class="text-gray-500">No voice connections yet</div>
                                </div>
                            </div>

                            <!-- Debug Info -->
                            <div id="debugInfo" class="mt-3 text-xs bg-gray-100 p-2 rounded">
                                <div class="font-medium text-gray-700 mb-1">🔧 Debug Info:</div>
                                <div id="debugContent" class="text-gray-600 space-y-1">
                                    <div>WebRTC Status: <span id="webrtcStatus">Initializing...</span></div>
                                    <div>Local Stream: <span id="localStreamStatus">None</span></div>
                                    <div>Peer Connections: <span id="peerConnectionCount">0</span></div>
                                    <div>Remote Streams: <span id="remoteStreamCount">0</span></div>
                                </div>
                                <button onclick="debugWebRTC()" class="mt-2 px-2 py-1 bg-blue-500 text-white text-xs rounded">
                                    Debug WebRTC
                                </button>
                            </div>
                        </div>
                    @else
                        <div class="text-center text-gray-300">
                            <p class="mb-4">Join the room to participate in voice chat</p>
                            <button onclick="joinRoom()"
                                    class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600">
                                Join Room
                            </button>
                        </div>
                    @endif
                @else
                    <div class="text-center text-gray-300">
                        <p>Please login to participate in voice chat</p>
                    </div>
                @endauth
            </div>
        </div>

        <!-- Participants Sidebar -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Participants</h3>
                    <button onclick="refreshParticipants()" class="text-blue-500 hover:text-blue-700">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>

                <div id="participantsList" class="space-y-3">
                    <!-- Participants will be loaded here via JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Messages -->
    <div class="mt-6 bg-white rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Room Chat</h3>
            <div class="flex items-center space-x-2">
                <button onclick="refreshMessages()" class="text-blue-500 hover:text-blue-700">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                    </svg>
                </button>
                @auth
                    @if($currentParticipant)
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" id="anonymousMode" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                            <span class="text-sm text-gray-600">Anonymous</span>
                        </label>
                    @endif
                @endauth
            </div>
        </div>

        <div id="chatMessages" class="bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto mb-4 space-y-2">
            <div id="chatPlaceholder" class="text-gray-500 text-center">
                Loading messages...
            </div>
        </div>

        @auth
            @if($currentParticipant)
                <div class="flex space-x-2">
                    <input type="text" id="chatInput" placeholder="Type a message..."
                           class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           maxlength="1000">
                    <button onclick="sendMessage()"
                            class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
                        Send
                    </button>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <span id="charCount">0</span>/1000 characters
                </div>
            @else
                <p class="text-gray-500 text-center">Join the room to participate in chat</p>
            @endif
        @else
            <p class="text-gray-500 text-center">Login to participate in chat</p>
        @endauth
    </div>

    <div class="mt-6 text-center">
        <a href="{{ route('voice-rooms.index') }}"
           class="text-blue-600 hover:text-blue-800">
            ← Back to Voice Rooms
        </a>
    </div>
</div>

<!-- Invite Friends Modal -->
<div id="inviteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Invite Friends</h3>
                    <button onclick="hideInviteModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div id="friendsList" class="space-y-2 max-h-64 overflow-y-auto">
                    <!-- Friends will be loaded here -->
                </div>

                <div class="mt-6 flex justify-end space-x-3">
                    <button onclick="hideInviteModal()"
                            class="px-4 py-2 text-gray-600 hover:text-gray-800">
                        Cancel
                    </button>
                    <button onclick="sendInvitations()"
                            class="bg-purple-500 text-white px-4 py-2 rounded-lg hover:bg-purple-600">
                        Send Invitations
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let isMuted = true;
let mediaStream = null;
let selectedFriends = [];
const roomId = {{ $voiceRoom->id }};
const currentUserId = {{ auth()->id() ?? 'null' }};

// WebRTC variables
let peerConnections = new Map(); // Map of userId -> RTCPeerConnection
let remoteStreams = new Map(); // Map of userId -> MediaStream
let localAudioElement = null;
let isWebRTCSupported = false;

// WebRTC configuration
const rtcConfiguration = {
    iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
        { urls: 'stun:stun2.l.google.com:19302' }
    ]
};

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadParticipants();
    setInterval(loadParticipants, 10000); // Refresh every 10 seconds

    // Initialize WebRTC
    initializeWebRTC();

    // Initialize chat if user is a participant
    @if($currentParticipant)
        initializeChat();
        startSignalingPolling();
    @endif
});

// Initialize WebRTC
function initializeWebRTC() {
    // Check WebRTC support
    isWebRTCSupported = !!(window.RTCPeerConnection && navigator.mediaDevices && navigator.mediaDevices.getUserMedia);

    if (!isWebRTCSupported) {
        console.warn('WebRTC is not supported in this browser');
        return;
    }

    console.log('WebRTC initialized successfully');

    // Enable audio context on first user interaction
    enableAudioContextOnInteraction();

    // Update debug info
    updateDebugInfo();
}

// Enable audio context on user interaction (required by browsers)
function enableAudioContextOnInteraction() {
    const enableAudio = () => {
        // Create and resume audio context
        if (!window.audioContextEnabled) {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                if (audioContext.state === 'suspended') {
                    audioContext.resume().then(() => {
                        console.log('Audio context resumed');
                        window.audioContextEnabled = true;
                    });
                } else {
                    window.audioContextEnabled = true;
                }
            } catch (error) {
                console.warn('Could not create audio context:', error);
            }
        }

        // Remove event listeners after first interaction
        document.removeEventListener('click', enableAudio);
        document.removeEventListener('touchstart', enableAudio);
    };

    document.addEventListener('click', enableAudio);
    document.addEventListener('touchstart', enableAudio);
}

// Debug WebRTC function
function debugWebRTC() {
    console.log('=== WebRTC Debug Info ===');
    console.log('WebRTC Supported:', isWebRTCSupported);
    console.log('Current User ID:', currentUserId);
    console.log('Local Stream:', mediaStream);
    console.log('Peer Connections:', peerConnections);
    console.log('Remote Streams:', remoteStreams);

    // Check audio elements
    const audioElements = document.querySelectorAll('[id^="remote-audio-"]');
    console.log('Audio Elements:', audioElements);
    audioElements.forEach(audio => {
        console.log(`Audio ${audio.id}:`, {
            srcObject: audio.srcObject,
            readyState: audio.readyState,
            paused: audio.paused,
            volume: audio.volume,
            muted: audio.muted
        });
    });

    // Check peer connection states
    peerConnections.forEach((pc, userId) => {
        console.log(`Peer Connection ${userId}:`, {
            connectionState: pc.connectionState,
            iceConnectionState: pc.iceConnectionState,
            signalingState: pc.signalingState,
            senders: pc.getSenders().map(s => ({track: s.track?.kind, enabled: s.track?.enabled})),
            receivers: pc.getReceivers().map(r => ({track: r.track?.kind, enabled: r.track?.enabled}))
        });
    });

    updateDebugInfo();
}

// Update debug info display
function updateDebugInfo() {
    const webrtcStatus = document.getElementById('webrtcStatus');
    const localStreamStatus = document.getElementById('localStreamStatus');
    const peerConnectionCount = document.getElementById('peerConnectionCount');
    const remoteStreamCount = document.getElementById('remoteStreamCount');

    if (webrtcStatus) webrtcStatus.textContent = isWebRTCSupported ? 'Supported' : 'Not Supported';
    if (localStreamStatus) localStreamStatus.textContent = mediaStream ? 'Active' : 'None';
    if (peerConnectionCount) peerConnectionCount.textContent = peerConnections.size;
    if (remoteStreamCount) remoteStreamCount.textContent = remoteStreams.size;
}

// Start polling for WebRTC signals
function startSignalingPolling() {
    if (!currentUserId) return;

    setInterval(async () => {
        try {
            const response = await fetch(`/voice-rooms/${roomId}/webrtc/signals`, {
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();
            if (data.success && data.signals.length > 0) {
                for (const signal of data.signals) {
                    await handleSignal(signal);
                }
            }
        } catch (error) {
            console.error('Error polling signals:', error);
        }
    }, 1000); // Poll every second
}

// Handle incoming WebRTC signals
async function handleSignal(signal) {
    console.log('Received signal:', signal);

    const { from_user_id, signal_type, signal_data } = signal;

    try {
        switch (signal_type) {
            case 'offer':
                await handleOffer(from_user_id, signal_data);
                break;
            case 'answer':
                await handleAnswer(from_user_id, signal_data);
                break;
            case 'ice-candidate':
                await handleIceCandidate(from_user_id, signal_data);
                break;
            case 'join':
                await handleUserJoin(from_user_id);
                break;
            case 'leave':
                await handleUserLeave(from_user_id);
                break;
        }
    } catch (error) {
        console.error('Error handling signal:', error);
    }
}

// Send WebRTC signal
async function sendSignal(signalType, signalData, toUserId = null) {
    try {
        const response = await fetch(`/voice-rooms/${roomId}/webrtc/signal`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                signal_type: signalType,
                signal_data: signalData,
                to_user_id: toUserId
            })
        });

        const data = await response.json();
        if (!data.success) {
            console.error('Failed to send signal:', data.message);
        }
    } catch (error) {
        console.error('Error sending signal:', error);
    }
}

// Create peer connection for a user
function createPeerConnection(userId) {
    if (peerConnections.has(userId)) {
        return peerConnections.get(userId);
    }

    const peerConnection = new RTCPeerConnection(rtcConfiguration);

    // Handle ICE candidates
    peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
            sendSignal('ice-candidate', {
                candidate: event.candidate.candidate,
                sdpMLineIndex: event.candidate.sdpMLineIndex,
                sdpMid: event.candidate.sdpMid
            }, userId);
        }
    };

    // Handle remote stream
    peerConnection.ontrack = (event) => {
        console.log('🎵 Received remote track from user:', userId, event);
        console.log('Track kind:', event.track.kind);
        console.log('Track enabled:', event.track.enabled);
        console.log('Track readyState:', event.track.readyState);
        console.log('Streams:', event.streams);

        if (event.streams && event.streams.length > 0) {
            const remoteStream = event.streams[0];
            console.log('Remote stream tracks:', remoteStream.getTracks().map(t => ({kind: t.kind, enabled: t.enabled})));
            remoteStreams.set(userId, remoteStream);
            playRemoteAudio(userId, remoteStream);
        } else {
            console.warn('No streams in track event');
        }
    };

    // Handle connection state changes
    peerConnection.onconnectionstatechange = () => {
        console.log(`Connection state with user ${userId}:`, peerConnection.connectionState);
        if (peerConnection.connectionState === 'failed') {
            console.log('Connection failed, attempting to restart ICE');
            peerConnection.restartIce();
        }
        updateParticipantsList();
    };

    // Add local stream if available
    if (mediaStream) {
        mediaStream.getTracks().forEach(track => {
            peerConnection.addTrack(track, mediaStream);
        });
    }

    peerConnections.set(userId, peerConnection);
    return peerConnection;
}

// Handle incoming offer
async function handleOffer(fromUserId, offerData) {
    console.log('Handling offer from user:', fromUserId);

    const peerConnection = createPeerConnection(fromUserId);

    try {
        // Add local stream to peer connection before setting remote description
        if (mediaStream) {
            console.log('Adding local stream to peer connection for offer response');
            mediaStream.getTracks().forEach(track => {
                const sender = peerConnection.getSenders().find(s => s.track && s.track.kind === track.kind);
                if (!sender) {
                    peerConnection.addTrack(track, mediaStream);
                    console.log('Added track to peer connection:', track.kind);
                }
            });
        }

        await peerConnection.setRemoteDescription(new RTCSessionDescription(offerData));
        const answer = await peerConnection.createAnswer();
        await peerConnection.setLocalDescription(answer);

        console.log('Sending answer to user:', fromUserId);
        sendSignal('answer', {
            type: answer.type,
            sdp: answer.sdp
        }, fromUserId);
    } catch (error) {
        console.error('Error handling offer:', error);
    }
}

// Handle incoming answer
async function handleAnswer(fromUserId, answerData) {
    console.log('Handling answer from user:', fromUserId);

    const peerConnection = peerConnections.get(fromUserId);
    if (!peerConnection) {
        console.error('No peer connection found for user:', fromUserId);
        return;
    }

    try {
        await peerConnection.setRemoteDescription(new RTCSessionDescription(answerData));
    } catch (error) {
        console.error('Error handling answer:', error);
    }
}

// Handle ICE candidate
async function handleIceCandidate(fromUserId, candidateData) {
    const peerConnection = peerConnections.get(fromUserId);
    if (!peerConnection) {
        console.error('No peer connection found for user:', fromUserId);
        return;
    }

    try {
        const candidate = new RTCIceCandidate({
            candidate: candidateData.candidate,
            sdpMLineIndex: candidateData.sdpMLineIndex,
            sdpMid: candidateData.sdpMid
        });
        await peerConnection.addIceCandidate(candidate);
    } catch (error) {
        console.error('Error handling ICE candidate:', error);
    }
}

// Handle user joining
async function handleUserJoin(userId) {
    console.log('User joined:', userId);

    // Skip if it's the current user
    if (userId == currentUserId) {
        console.log('Skipping self connection');
        return;
    }

    // Create peer connection regardless of local stream status
    const peerConnection = createPeerConnection(userId);

    // If we have a local stream, create an offer
    if (mediaStream) {
        console.log('Creating offer for new user with local stream');
        try {
            const offer = await peerConnection.createOffer();
            await peerConnection.setLocalDescription(offer);

            console.log('Sending offer to user:', userId);
            sendSignal('offer', {
                type: offer.type,
                sdp: offer.sdp
            }, userId);
        } catch (error) {
            console.error('Error creating offer for new user:', error);
        }
    } else {
        console.log('No local stream available, waiting for remote offer from user:', userId);
    }
}

// Handle user leaving
async function handleUserLeave(userId) {
    console.log('User left:', userId);

    // Close peer connection
    const peerConnection = peerConnections.get(userId);
    if (peerConnection) {
        peerConnection.close();
        peerConnections.delete(userId);
    }

    // Remove remote stream
    remoteStreams.delete(userId);

    // Remove audio element
    const audioElement = document.getElementById(`remote-audio-${userId}`);
    if (audioElement) {
        audioElement.remove();
    }

    // Update UI
    updateParticipantsList();
}

// Play remote audio stream
function playRemoteAudio(userId, stream) {
    console.log(`Setting up remote audio for user ${userId}`, stream);

    // Remove existing audio element if any
    const existingAudio = document.getElementById(`remote-audio-${userId}`);
    if (existingAudio) {
        existingAudio.remove();
    }

    // Create new audio element
    const audioElement = document.createElement('audio');
    audioElement.id = `remote-audio-${userId}`;
    audioElement.srcObject = stream;
    audioElement.autoplay = true;
    audioElement.style.display = 'none';
    audioElement.volume = 1.0; // Full volume
    audioElement.controls = false;
    audioElement.muted = false;

    // Add comprehensive error handling
    audioElement.onerror = (error) => {
        console.error(`Audio error for user ${userId}:`, error);
        console.error('Audio element error details:', audioElement.error);
    };

    audioElement.onloadedmetadata = () => {
        console.log(`Audio metadata loaded for user ${userId}`);
        console.log('Audio element ready state:', audioElement.readyState);
        console.log('Audio element duration:', audioElement.duration);
        updateParticipantsList();
    };

    audioElement.oncanplay = () => {
        console.log(`Audio can play for user ${userId}`);
        // Force play to ensure audio starts
        const playPromise = audioElement.play();
        if (playPromise !== undefined) {
            playPromise.then(() => {
                console.log(`✅ Successfully started playing audio for user ${userId}`);
            }).catch(error => {
                console.error(`❌ Failed to play audio for user ${userId}:`, error);
                console.log('This is likely due to browser autoplay policy. Audio will start after user interaction.');

                // Create a one-time click handler to start audio
                const startAudio = () => {
                    audioElement.play().then(() => {
                        console.log(`✅ Audio started after user interaction for user ${userId}`);
                    }).catch(e => {
                        console.error('Still failed to play after interaction:', e);
                    });
                    document.removeEventListener('click', startAudio);
                };
                document.addEventListener('click', startAudio);
            });
        }
    };

    audioElement.onplaying = () => {
        console.log(`Audio is now playing for user ${userId}`);
    };

    audioElement.onpause = () => {
        console.log(`Audio paused for user ${userId}`);
    };

    // Add to page
    document.body.appendChild(audioElement);

    console.log(`Remote audio element created for user ${userId}`);
    updateParticipantsList();
    updateDebugInfo();
}

// Update the participants list UI
function updateParticipantsList() {
    const participantsList = document.getElementById('participantsList');
    if (!participantsList) return;

    const connections = Array.from(peerConnections.entries());

    if (connections.length === 0) {
        participantsList.innerHTML = '<div class="text-gray-500">No voice connections yet</div>';
        return;
    }

    participantsList.innerHTML = connections.map(([userId, peerConnection]) => {
        const connectionState = peerConnection.connectionState;
        const hasAudio = remoteStreams.has(userId);

        let statusIcon = '🔴'; // Disconnected
        let statusText = 'Disconnected';

        if (connectionState === 'connected') {
            statusIcon = hasAudio ? '🟢' : '🟡';
            statusText = hasAudio ? 'Connected & Audio' : 'Connected';
        } else if (connectionState === 'connecting') {
            statusIcon = '🟡';
            statusText = 'Connecting...';
        }

        return `
            <div class="flex items-center justify-between bg-gray-100 p-2 rounded">
                <span>User ${userId}</span>
                <span class="text-xs">${statusIcon} ${statusText}</span>
            </div>
        `;
    }).join('');
}

// Get connected participants and establish connections
async function connectToExistingParticipants() {
    try {
        const response = await fetch(`/voice-rooms/${roomId}/webrtc/participants`, {
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();
        if (data.success) {
            for (const participant of data.participants) {
                await handleUserJoin(participant.user_id);
            }
        }
    } catch (error) {
        console.error('Error connecting to existing participants:', error);
    }
}

// Join room function
async function joinRoom() {
    try {
        const response = await fetch(`/voice-rooms/${roomId}/join`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            location.reload(); // Reload to update UI
        } else {
            alert(data.message);
        }
    } catch (error) {
        console.error('Error joining room:', error);
        alert('Failed to join room');
    }
}

// Leave room function
async function leaveRoom() {
    if (confirm('Are you sure you want to leave this room?')) {
        try {
            // Stop microphone if active
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
            }

            const response = await fetch(`/voice-rooms/${roomId}/leave`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                location.reload(); // Reload to update UI
            } else {
                alert(data.message);
            }
        } catch (error) {
            console.error('Error leaving room:', error);
            alert('Failed to leave room');
        }
    }
}

// Toggle microphone
async function toggleMicrophone() {
    const micButton = document.getElementById('micButton');
    const audioStatus = document.getElementById('audioStatus');

    console.log('toggleMicrophone called, current isMuted:', isMuted);

    try {
        const response = await fetch(`/voice-rooms/${roomId}/toggle-mute`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            if (data.is_muted) {
                // Stop microphone
                if (mediaStream) {
                    mediaStream.getTracks().forEach(track => track.stop());
                    mediaStream = null;
                }
                micButton.classList.remove('bg-green-500', 'hover:bg-green-600');
                micButton.classList.add('bg-red-500', 'hover:bg-red-600');
                audioStatus.textContent = 'Microphone is muted';
                isMuted = true;

                // Hide audio visualization
                const audioVisualization = document.getElementById('audioVisualization');
                if (audioVisualization) {
                    audioVisualization.classList.add('hidden');
                }

                // Stop audio visualization
                if (window.audioVisualizationInterval) {
                    clearInterval(window.audioVisualizationInterval);
                }

                // Close all peer connections and send leave signal
                if (isWebRTCSupported) {
                    peerConnections.forEach((peerConnection, userId) => {
                        peerConnection.close();
                    });
                    peerConnections.clear();
                    remoteStreams.clear();

                    // Remove all remote audio elements
                    document.querySelectorAll('[id^="remote-audio-"]').forEach(el => el.remove());

                    // Send leave signal
                    sendSignal('leave', { userId: currentUserId });
                }
            } else {
                // Start microphone
                try {
                    console.log('Requesting microphone access...');

                    // Check if getUserMedia is supported
                    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                        throw new Error('getUserMedia is not supported in this browser');
                    }

                    mediaStream = await navigator.mediaDevices.getUserMedia({
                        audio: {
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true
                        }
                    });

                    console.log('Microphone access granted');
                    micButton.classList.remove('bg-red-500', 'hover:bg-red-600');
                    micButton.classList.add('bg-green-500', 'hover:bg-green-600');
                    audioStatus.textContent = 'Microphone is active - WebRTC Voice Chat';
                    isMuted = false;

                    // Show audio visualization
                    const audioVisualization = document.getElementById('audioVisualization');
                    if (audioVisualization) {
                        audioVisualization.classList.remove('hidden');
                        startAudioVisualization();
                    }

                    // Create audio context for visualization
                    try {
                        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        const source = audioContext.createMediaStreamSource(mediaStream);
                        const analyser = audioContext.createAnalyser();
                        source.connect(analyser);

                        // Store for visualization
                        window.audioAnalyser = analyser;
                        console.log('Audio context created successfully');
                    } catch (audioError) {
                        console.warn('Could not create audio context:', audioError);
                    }

                    // Add audio tracks to existing peer connections
                    if (isWebRTCSupported) {
                        mediaStream.getTracks().forEach(track => {
                            peerConnections.forEach((peerConnection, userId) => {
                                const sender = peerConnection.getSenders().find(s => s.track && s.track.kind === track.kind);
                                if (sender) {
                                    sender.replaceTrack(track);
                                } else {
                                    peerConnection.addTrack(track, mediaStream);
                                }
                            });
                        });

                        // Connect to existing participants
                        await connectToExistingParticipants();

                        // Send join signal
                        sendSignal('join', { userId: currentUserId });
                    }

                    // Update debug info
                    updateDebugInfo();
                } catch (error) {
                    console.error('Error accessing microphone:', error);
                    let errorMessage = 'Could not access microphone. ';

                    if (error.name === 'NotAllowedError') {
                        errorMessage += 'Please allow microphone access in your browser settings.';
                    } else if (error.name === 'NotFoundError') {
                        errorMessage += 'No microphone found. Please connect a microphone.';
                    } else if (error.name === 'NotReadableError') {
                        errorMessage += 'Microphone is already in use by another application.';
                    } else {
                        errorMessage += 'Please check your permissions and try again.';
                    }

                    alert(errorMessage);
                }
            }
        } else {
            alert(data.message);
        }
    } catch (error) {
        console.error('Error toggling mute:', error);
        alert('Failed to toggle mute');
    }
}

// Load participants
async function loadParticipants() {
    try {
        const response = await fetch(`/voice-rooms/${roomId}/participants`);
        const data = await response.json();

        if (data.success) {
            displayParticipants(data.participants);
            document.getElementById('participantCount').textContent = data.total_count;
        }
    } catch (error) {
        console.error('Error loading participants:', error);
    }
}

// Display participants
function displayParticipants(participants) {
    const participantsList = document.getElementById('participantsList');
    participantsList.innerHTML = '';

    participants.forEach(participant => {
        const participantDiv = document.createElement('div');
        participantDiv.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg';

        participantDiv.innerHTML = `
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                    <span class="text-white font-medium">${participant.name.charAt(0)}</span>
                </div>
                <div>
                    <div class="flex items-center space-x-1">
                        <p class="font-medium text-gray-900">${participant.name}</p>
                        ${participant.is_verified ? '<svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' : ''}
                        ${participant.is_friend ? '<span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Friend</span>' : ''}
                    </div>
                    <p class="text-sm text-gray-500">${participant.status} • ${participant.role}</p>
                </div>
            </div>
            ${participant.role === 'host' || participant.role === 'moderator' ? `
                <div class="flex space-x-1">
                    <button onclick="muteParticipant(${participant.user_id})"
                            class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded hover:bg-yellow-200">
                        ${participant.is_muted ? 'Unmute' : 'Mute'}
                    </button>
                    ${participant.role !== 'host' ? `
                        <button onclick="kickParticipant(${participant.user_id})"
                                class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded hover:bg-red-200">
                            Kick
                        </button>
                    ` : ''}
                </div>
            ` : ''}
        `;

        participantsList.appendChild(participantDiv);
    });
}

// Refresh participants
function refreshParticipants() {
    loadParticipants();
}

// Show invite modal
async function showInviteModal() {
    try {
        const response = await fetch(`/voice-rooms/${roomId}/invitable-friends`);
        const data = await response.json();

        if (data.success) {
            displayInvitableFriends(data.friends);
            document.getElementById('inviteModal').classList.remove('hidden');
        } else {
            alert('Failed to load friends');
        }
    } catch (error) {
        console.error('Error loading friends:', error);
        alert('Failed to load friends');
    }
}

// Hide invite modal
function hideInviteModal() {
    document.getElementById('inviteModal').classList.add('hidden');
    selectedFriends = [];
}

// Display invitable friends
function displayInvitableFriends(friends) {
    const friendsList = document.getElementById('friendsList');
    friendsList.innerHTML = '';

    if (friends.length === 0) {
        friendsList.innerHTML = '<p class="text-gray-500 text-center">No friends available to invite</p>';
        return;
    }

    friends.forEach(friend => {
        const friendDiv = document.createElement('div');
        friendDiv.className = 'flex items-center space-x-3 p-2 hover:bg-gray-50 rounded';

        friendDiv.innerHTML = `
            <input type="checkbox" id="friend_${friend.id}" value="${friend.id}"
                   onchange="toggleFriendSelection(${friend.id})"
                   class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span class="text-white text-sm font-medium">${friend.name.charAt(0)}</span>
            </div>
            <label for="friend_${friend.id}" class="flex-1 cursor-pointer">
                <p class="font-medium text-gray-900">${friend.name}</p>
                <p class="text-sm text-gray-500">@${friend.username}</p>
            </label>
        `;

        friendsList.appendChild(friendDiv);
    });
}

// Toggle friend selection
function toggleFriendSelection(friendId) {
    const checkbox = document.getElementById(`friend_${friendId}`);
    if (checkbox.checked) {
        selectedFriends.push(friendId);
    } else {
        selectedFriends = selectedFriends.filter(id => id !== friendId);
    }
}

// Send invitations
async function sendInvitations() {
    if (selectedFriends.length === 0) {
        alert('Please select at least one friend to invite');
        return;
    }

    try {
        const response = await fetch(`/voice-rooms/${roomId}/invite-friends`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                friend_ids: selectedFriends
            })
        });

        const data = await response.json();

        if (data.success) {
            alert(data.message);
            hideInviteModal();
        } else {
            alert('Failed to send invitations');
        }
    } catch (error) {
        console.error('Error sending invitations:', error);
        alert('Failed to send invitations');
    }
}

// Mute participant (host/moderator only)
async function muteParticipant(userId) {
    try {
        const response = await fetch(`/voice-rooms/${roomId}/mute/${userId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            loadParticipants(); // Refresh participants list
        } else {
            alert(data.message);
        }
    } catch (error) {
        console.error('Error muting participant:', error);
        alert('Failed to mute participant');
    }
}

// Kick participant (host/moderator only)
async function kickParticipant(userId) {
    if (confirm('Are you sure you want to kick this participant?')) {
        try {
            const response = await fetch(`/voice-rooms/${roomId}/kick/${userId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                loadParticipants(); // Refresh participants list
                document.getElementById('participantCount').textContent = data.participants_count;
            } else {
                alert(data.message);
            }
        } catch (error) {
            console.error('Error kicking participant:', error);
            alert('Failed to kick participant');
        }
    }
}

// Chat functionality
let lastMessageId = 0;
let isLoadingMessages = false;

// Initialize chat
function initializeChat() {
    loadMessages();
    setInterval(loadMessages, 3000); // Check for new messages every 3 seconds

    // Character counter
    const chatInput = document.getElementById('chatInput');
    const charCount = document.getElementById('charCount');

    if (chatInput && charCount) {
        chatInput.addEventListener('input', function() {
            charCount.textContent = this.value.length;
        });
    }
}

// Load messages
async function loadMessages() {
    if (isLoadingMessages) return;
    isLoadingMessages = true;

    try {
        console.log('Loading messages, last_message_id:', lastMessageId);
        const response = await fetch(`/voice-rooms/${roomId}/messages?last_message_id=${lastMessageId}`, {
            headers: {
                'Accept': 'application/json'
            }
        });

        console.log('Load messages response status:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Load messages error:', errorText);
            return;
        }

        const data = await response.json();
        console.log('Load messages data:', data);

        if (data.success && data.messages && data.messages.length > 0) {
            displayMessages(data.messages);
            lastMessageId = data.last_message_id || lastMessageId;
        }

        // Hide placeholder if we have messages
        if (lastMessageId > 0) {
            const placeholder = document.getElementById('chatPlaceholder');
            if (placeholder) {
                placeholder.style.display = 'none';
            }
        }
    } catch (error) {
        console.error('Error loading messages:', error);
    } finally {
        isLoadingMessages = false;
    }
}

// Display messages
function displayMessages(messages) {
    const chatMessages = document.getElementById('chatMessages');
    const shouldScrollToBottom = chatMessages.scrollTop + chatMessages.clientHeight >= chatMessages.scrollHeight - 10;

    messages.forEach(message => {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message-item ${message.is_own_message ? 'own-message' : 'other-message'}`;
        messageDiv.setAttribute('data-message-id', message.id);

        let messageContent = '';

        if (message.message_type === 'system') {
            messageContent = `
                <div class="text-center text-sm text-gray-500 italic py-2">
                    ${message.message}
                </div>
            `;
        } else {
            messageContent = `
                <div class="flex items-start space-x-2 ${message.is_own_message ? 'flex-row-reverse space-x-reverse' : ''}">
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <span class="text-white text-sm font-medium">${message.sender_name.charAt(0)}</span>
                    </div>
                    <div class="flex-1 ${message.is_own_message ? 'text-right' : ''}">
                        <div class="flex items-center space-x-1 ${message.is_own_message ? 'justify-end' : ''}">
                            <span class="font-medium text-sm text-gray-900">${message.sender_name}</span>
                            ${message.sender_verified ? '<svg class="w-3 h-3 text-blue-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' : ''}
                            <span class="text-xs text-gray-500">${message.created_at}</span>
                        </div>
                        <div class="mt-1">
                            <div class="inline-block px-3 py-2 rounded-lg ${message.is_own_message ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-900'} max-w-xs break-words">
                                ${message.message}
                            </div>
                        </div>
                        ${(message.can_edit || message.can_delete) ? `
                            <div class="mt-1 flex space-x-2 text-xs ${message.is_own_message ? 'justify-end' : ''}">
                                ${message.can_edit ? `<button onclick="editMessage(${message.id})" class="text-blue-500 hover:text-blue-700">Edit</button>` : ''}
                                ${message.can_delete ? `<button onclick="deleteMessage(${message.id})" class="text-red-500 hover:text-red-700">Delete</button>` : ''}
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        messageDiv.innerHTML = messageContent;
        chatMessages.appendChild(messageDiv);
    });

    // Auto-scroll to bottom if user was already at bottom
    if (shouldScrollToBottom) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

// Send message
async function sendMessage() {
    const chatInput = document.getElementById('chatInput');
    const anonymousMode = document.getElementById('anonymousMode');
    const message = chatInput.value.trim();

    if (!message) {
        console.log('No message to send');
        return;
    }

    console.log('Sending message:', message);

    try {
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (!csrfToken) {
            throw new Error('CSRF token not found');
        }

        const response = await fetch(`/voice-rooms/${roomId}/messages`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                message: message,
                is_anonymous: anonymousMode ? anonymousMode.checked : false
            })
        });

        console.log('Response status:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Response error:', errorText);
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const data = await response.json();
        console.log('Response data:', data);

        if (data.success) {
            chatInput.value = '';
            const charCountElement = document.getElementById('charCount');
            if (charCountElement) {
                charCountElement.textContent = '0';
            }

            // Add the message immediately to the chat
            if (data.message_data) {
                displayMessages([data.message_data]);
                lastMessageId = Math.max(lastMessageId, data.message_data.id);
            }
        } else {
            console.error('Server error:', data.message);
            alert(data.message || 'Failed to send message');
        }
    } catch (error) {
        console.error('Error sending message:', error);
        alert('Failed to send message: ' + error.message);
    }
}

// Refresh messages
function refreshMessages() {
    lastMessageId = 0;
    document.getElementById('chatMessages').innerHTML = '<div id="chatPlaceholder" class="text-gray-500 text-center">Loading messages...</div>';
    loadMessages();
}

// Delete message
async function deleteMessage(messageId) {
    if (!confirm('Are you sure you want to delete this message?')) return;

    try {
        const response = await fetch(`/voice-rooms/${roomId}/messages/${messageId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            // Remove the message from display
            const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
            if (messageElement) {
                messageElement.remove();
            }
        } else {
            alert(data.message);
        }
    } catch (error) {
        console.error('Error deleting message:', error);
        alert('Failed to delete message');
    }
}

// Edit message (placeholder for future implementation)
function editMessage(messageId) {
    alert('Message editing will be implemented in a future update');
}

// Handle Enter key in chat input
document.getElementById('chatInput')?.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        sendMessage();
    }
});

// Audio visualization function
function startAudioVisualization() {
    const audioBars = document.querySelectorAll('.audio-bar');

    if (window.audioVisualizationInterval) {
        clearInterval(window.audioVisualizationInterval);
    }

    window.audioVisualizationInterval = setInterval(() => {
        if (window.audioAnalyser && !isMuted) {
            // Get audio data
            const bufferLength = window.audioAnalyser.frequencyBinCount;
            const dataArray = new Uint8Array(bufferLength);
            window.audioAnalyser.getByteFrequencyData(dataArray);

            // Calculate average volume
            let sum = 0;
            for (let i = 0; i < bufferLength; i++) {
                sum += dataArray[i];
            }
            const average = sum / bufferLength;

            // Update bars based on audio level
            audioBars.forEach((bar, index) => {
                const height = Math.max(10, (average / 255) * 100 + Math.random() * 20);
                bar.style.height = height + '%';

                // Color based on volume
                if (average > 50) {
                    bar.className = 'w-1 bg-red-500 rounded-full audio-bar';
                } else if (average > 25) {
                    bar.className = 'w-1 bg-yellow-500 rounded-full audio-bar';
                } else {
                    bar.className = 'w-1 bg-green-500 rounded-full audio-bar';
                }
            });
        } else {
            // Animate bars randomly when no real audio data
            audioBars.forEach((bar) => {
                const height = Math.random() * 50 + 10;
                bar.style.height = height + '%';
            });
        }
    }, 100);
}

// Auto-refresh messages every 3 seconds
setInterval(loadMessages, 3000);

// Load initial messages
loadMessages();

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop());
    }
    if (window.audioVisualizationInterval) {
        clearInterval(window.audioVisualizationInterval);
    }
});
</script>
@endsection