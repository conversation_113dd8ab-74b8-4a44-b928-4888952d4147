@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex justify-between items-start mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ $voiceRoom->name }}</h1>
                @if($voiceRoom->description)
                    <p class="text-gray-600 mb-4">{{ $voiceRoom->description }}</p>
                @endif
                
                <div class="flex items-center space-x-4 text-sm text-gray-500">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                        </svg>
                        Host: {{ $voiceRoom->host->name }}
                    </div>
                    
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                        </svg>
                        {{ $voiceRoom->current_participants }}/{{ $voiceRoom->max_participants }} participants
                    </div>
                    
                    @if($voiceRoom->category)
                        <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded">
                            {{ $voiceRoom->category }}
                        </span>
                    @endif
                    
                    <span class="flex items-center text-green-600">
                        <span class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
                        Live
                    </span>
                </div>
            </div>
            
            @auth
                @if($voiceRoom->host_id === auth()->id())
                    <div class="flex items-center space-x-2">
                        <a href="{{ route('voice-rooms.edit', $voiceRoom) }}" 
                           class="text-green-600 hover:text-green-800">
                            Edit Room
                        </a>
                        <form method="POST" action="{{ route('voice-rooms.destroy', $voiceRoom) }}" 
                              class="inline" onsubmit="return confirm('Are you sure you want to end this room?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="text-red-600 hover:text-red-800">
                                End Room
                            </button>
                        </form>
                    </div>
                @endif
            @endauth
        </div>
    </div>

    <!-- Voice Chat Interface -->
    <div class="bg-gray-900 rounded-lg p-6 mb-6">
        <div class="text-center mb-6">
            <h2 class="text-2xl font-bold text-white mb-2">Voice Chat</h2>
            <p class="text-gray-300">Click the microphone to start talking</p>
        </div>

        <!-- Microphone Controls -->
        <div class="flex justify-center items-center space-x-4 mb-6">
            <button id="micButton" onclick="toggleMicrophone()" 
                    class="w-16 h-16 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center transition-colors">
                <svg id="micIcon" class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"></path>
                </svg>
            </button>
            
            <button onclick="leaveRoom()" 
                    class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg">
                Leave Room
            </button>
        </div>

        <!-- Audio Status -->
        <div class="text-center">
            <p id="audioStatus" class="text-gray-300">Microphone is muted</p>
        </div>

        <!-- Participants List -->
        <div class="mt-8">
            <h3 class="text-lg font-semibold text-white mb-4">Participants</h3>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <div class="bg-gray-800 rounded-lg p-4 text-center">
                    <div class="w-12 h-12 bg-blue-500 rounded-full mx-auto mb-2 flex items-center justify-center">
                        <span class="text-white font-medium">{{ substr($voiceRoom->host->name, 0, 1) }}</span>
                    </div>
                    <p class="text-white text-sm">{{ $voiceRoom->host->name }}</p>
                    <p class="text-gray-400 text-xs">Host</p>
                </div>
                
                @auth
                    @if($voiceRoom->host_id !== auth()->id())
                        <div class="bg-gray-800 rounded-lg p-4 text-center">
                            <div class="w-12 h-12 bg-green-500 rounded-full mx-auto mb-2 flex items-center justify-center">
                                <span class="text-white font-medium">{{ substr(auth()->user()->name, 0, 1) }}</span>
                            </div>
                            <p class="text-white text-sm">{{ auth()->user()->name }}</p>
                            <p class="text-gray-400 text-xs">You</p>
                        </div>
                    @endif
                @endauth
            </div>
        </div>
    </div>

    <!-- Chat Messages (Optional) -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Room Chat</h3>
        <div class="bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto mb-4">
            <p class="text-gray-500 text-center">Chat messages will appear here...</p>
        </div>
        
        @auth
            <div class="flex space-x-2">
                <input type="text" id="chatInput" placeholder="Type a message..." 
                       class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <button onclick="sendMessage()" 
                        class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
                    Send
                </button>
            </div>
        @else
            <p class="text-gray-500 text-center">Login to participate in chat</p>
        @endauth
    </div>

    <div class="mt-6 text-center">
        <a href="{{ route('voice-rooms.index') }}" 
           class="text-blue-600 hover:text-blue-800">
            ← Back to Voice Rooms
        </a>
    </div>
</div>

<script>
let isMuted = true;
let mediaStream = null;

async function toggleMicrophone() {
    const micButton = document.getElementById('micButton');
    const micIcon = document.getElementById('micIcon');
    const audioStatus = document.getElementById('audioStatus');
    
    if (isMuted) {
        try {
            // Request microphone access
            mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
            
            // Update UI
            micButton.classList.remove('bg-red-500', 'hover:bg-red-600');
            micButton.classList.add('bg-green-500', 'hover:bg-green-600');
            audioStatus.textContent = 'Microphone is active';
            isMuted = false;
            
            console.log('Microphone activated');
            // Here you would typically connect to a WebRTC service
            
        } catch (error) {
            console.error('Error accessing microphone:', error);
            alert('Could not access microphone. Please check your permissions.');
        }
    } else {
        // Stop microphone
        if (mediaStream) {
            mediaStream.getTracks().forEach(track => track.stop());
            mediaStream = null;
        }
        
        // Update UI
        micButton.classList.remove('bg-green-500', 'hover:bg-green-600');
        micButton.classList.add('bg-red-500', 'hover:bg-red-600');
        audioStatus.textContent = 'Microphone is muted';
        isMuted = true;
        
        console.log('Microphone muted');
    }
}

function leaveRoom() {
    if (confirm('Are you sure you want to leave this room?')) {
        // Stop microphone if active
        if (mediaStream) {
            mediaStream.getTracks().forEach(track => track.stop());
        }
        
        // Redirect to rooms list
        window.location.href = '{{ route("voice-rooms.index") }}';
    }
}

function sendMessage() {
    const chatInput = document.getElementById('chatInput');
    const message = chatInput.value.trim();
    
    if (message) {
        // Here you would typically send the message via WebSocket
        console.log('Sending message:', message);
        chatInput.value = '';
        
        // For demo purposes, just show an alert
        alert('Chat functionality will be implemented with WebSocket integration');
    }
}

// Handle Enter key in chat input
document.getElementById('chatInput')?.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        sendMessage();
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop());
    }
});
</script>
@endsection