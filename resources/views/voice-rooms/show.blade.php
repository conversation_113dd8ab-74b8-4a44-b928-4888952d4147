@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto">
    <!-- Room Header -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex justify-between items-start mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ $voiceRoom->name }}</h1>
                @if($voiceRoom->description)
                    <p class="text-gray-600 mb-4">{{ $voiceRoom->description }}</p>
                @endif

                <div class="flex items-center space-x-4 text-sm text-gray-500">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                        </svg>
                        Host: {{ $voiceRoom->host->name }}
                        @if($voiceRoom->host->is_verified)
                            <svg class="w-4 h-4 ml-1 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        @endif
                    </div>

                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                        </svg>
                        <span id="participantCount">{{ $voiceRoom->current_participants }}</span>/{{ $voiceRoom->max_participants }} participants
                    </div>

                    @if($voiceRoom->category)
                        <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded">
                            {{ $voiceRoom->category }}
                        </span>
                    @endif

                    <span class="flex items-center text-green-600">
                        <span class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
                        Live
                    </span>
                </div>
            </div>
            
            @auth
                <div class="flex items-center space-x-2">
                    @if($voiceRoom->host_id === auth()->id())
                        <a href="{{ route('voice-rooms.edit', $voiceRoom) }}"
                           class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                            Edit Room
                        </a>
                        <form method="POST" action="{{ route('voice-rooms.destroy', $voiceRoom) }}"
                              class="inline" onsubmit="return confirm('Are you sure you want to end this room?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600">
                                End Room
                            </button>
                        </form>
                    @else
                        @if($currentParticipant)
                            <button id="leaveRoomBtn" onclick="leaveRoom()"
                                    class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600">
                                Leave Room
                            </button>
                        @else
                            <button id="joinRoomBtn" onclick="joinRoom()"
                                    class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600">
                                Join Room
                            </button>
                        @endif
                    @endif

                    <!-- Invite Friends Button -->
                    <button id="inviteFriendsBtn" onclick="showInviteModal()"
                            class="bg-purple-500 text-white px-4 py-2 rounded-lg hover:bg-purple-600">
                        Invite Friends
                    </button>
                </div>
            @endauth
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Voice Chat Interface -->
        <div class="lg:col-span-2">
            <div class="bg-gray-900 rounded-lg p-6">
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-bold text-white mb-2">Voice Chat</h2>
                    <p class="text-gray-300">Click the microphone to start talking</p>
                </div>

                @auth
                    @if($currentParticipant)
                        <!-- Microphone Controls -->
                        <div class="flex justify-center items-center space-x-4 mb-6">
                            <button id="micButton" onclick="toggleMicrophone()"
                                    class="w-16 h-16 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center transition-colors">
                                <svg id="micIcon" class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"></path>
                                    </svg>
                            </button>
                        </div>

                        <!-- Audio Status -->
                        <div class="text-center">
                            <p id="audioStatus" class="text-gray-300">
                                @if($currentParticipant->is_muted_by_host)
                                    Muted by host
                                @elseif($currentParticipant->is_self_muted)
                                    Microphone is muted
                                @else
                                    Microphone is active
                                @endif
                            </p>
                        </div>
                    @else
                        <div class="text-center text-gray-300">
                            <p class="mb-4">Join the room to participate in voice chat</p>
                            <button onclick="joinRoom()"
                                    class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600">
                                Join Room
                            </button>
                        </div>
                    @endif
                @else
                    <div class="text-center text-gray-300">
                        <p>Please login to participate in voice chat</p>
                    </div>
                @endauth
            </div>
        </div>

        <!-- Participants Sidebar -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Participants</h3>
                    <button onclick="refreshParticipants()" class="text-blue-500 hover:text-blue-700">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>

                <div id="participantsList" class="space-y-3">
                    <!-- Participants will be loaded here via JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Messages -->
    <div class="mt-6 bg-white rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Room Chat</h3>
            <div class="flex items-center space-x-2">
                <button onclick="refreshMessages()" class="text-blue-500 hover:text-blue-700">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                    </svg>
                </button>
                @auth
                    @if($currentParticipant)
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" id="anonymousMode" class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                            <span class="text-sm text-gray-600">Anonymous</span>
                        </label>
                    @endif
                @endauth
            </div>
        </div>

        <div id="chatMessages" class="bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto mb-4 space-y-2">
            <div id="chatPlaceholder" class="text-gray-500 text-center">
                Loading messages...
            </div>
        </div>

        @auth
            @if($currentParticipant)
                <div class="flex space-x-2">
                    <input type="text" id="chatInput" placeholder="Type a message..."
                           class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           maxlength="1000">
                    <button onclick="sendMessage()"
                            class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
                        Send
                    </button>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    <span id="charCount">0</span>/1000 characters
                </div>
            @else
                <p class="text-gray-500 text-center">Join the room to participate in chat</p>
            @endif
        @else
            <p class="text-gray-500 text-center">Login to participate in chat</p>
        @endauth
    </div>

    <div class="mt-6 text-center">
        <a href="{{ route('voice-rooms.index') }}"
           class="text-blue-600 hover:text-blue-800">
            ← Back to Voice Rooms
        </a>
    </div>
</div>

<!-- Invite Friends Modal -->
<div id="inviteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Invite Friends</h3>
                    <button onclick="hideInviteModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div id="friendsList" class="space-y-2 max-h-64 overflow-y-auto">
                    <!-- Friends will be loaded here -->
                </div>

                <div class="mt-6 flex justify-end space-x-3">
                    <button onclick="hideInviteModal()"
                            class="px-4 py-2 text-gray-600 hover:text-gray-800">
                        Cancel
                    </button>
                    <button onclick="sendInvitations()"
                            class="bg-purple-500 text-white px-4 py-2 rounded-lg hover:bg-purple-600">
                        Send Invitations
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let isMuted = true;
let mediaStream = null;
let selectedFriends = [];
const roomId = {{ $voiceRoom->id }};

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadParticipants();
    setInterval(loadParticipants, 10000); // Refresh every 10 seconds

    // Initialize chat if user is a participant
    @if($currentParticipant)
        initializeChat();
    @endif
});

// Join room function
async function joinRoom() {
    try {
        const response = await fetch(`/voice-rooms/${roomId}/join`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            location.reload(); // Reload to update UI
        } else {
            alert(data.message);
        }
    } catch (error) {
        console.error('Error joining room:', error);
        alert('Failed to join room');
    }
}

// Leave room function
async function leaveRoom() {
    if (confirm('Are you sure you want to leave this room?')) {
        try {
            // Stop microphone if active
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
            }

            const response = await fetch(`/voice-rooms/${roomId}/leave`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                location.reload(); // Reload to update UI
            } else {
                alert(data.message);
            }
        } catch (error) {
            console.error('Error leaving room:', error);
            alert('Failed to leave room');
        }
    }
}

// Toggle microphone
async function toggleMicrophone() {
    const micButton = document.getElementById('micButton');
    const audioStatus = document.getElementById('audioStatus');

    try {
        const response = await fetch(`/voice-rooms/${roomId}/toggle-mute`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            if (data.is_muted) {
                // Stop microphone
                if (mediaStream) {
                    mediaStream.getTracks().forEach(track => track.stop());
                    mediaStream = null;
                }
                micButton.classList.remove('bg-green-500', 'hover:bg-green-600');
                micButton.classList.add('bg-red-500', 'hover:bg-red-600');
                audioStatus.textContent = 'Microphone is muted';
                isMuted = true;
            } else {
                // Start microphone
                try {
                    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    micButton.classList.remove('bg-red-500', 'hover:bg-red-600');
                    micButton.classList.add('bg-green-500', 'hover:bg-green-600');
                    audioStatus.textContent = 'Microphone is active';
                    isMuted = false;
                } catch (error) {
                    console.error('Error accessing microphone:', error);
                    alert('Could not access microphone. Please check your permissions.');
                }
            }
        } else {
            alert(data.message);
        }
    } catch (error) {
        console.error('Error toggling mute:', error);
        alert('Failed to toggle mute');
    }
}

// Load participants
async function loadParticipants() {
    try {
        const response = await fetch(`/voice-rooms/${roomId}/participants`);
        const data = await response.json();

        if (data.success) {
            displayParticipants(data.participants);
            document.getElementById('participantCount').textContent = data.total_count;
        }
    } catch (error) {
        console.error('Error loading participants:', error);
    }
}

// Display participants
function displayParticipants(participants) {
    const participantsList = document.getElementById('participantsList');
    participantsList.innerHTML = '';

    participants.forEach(participant => {
        const participantDiv = document.createElement('div');
        participantDiv.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg';

        participantDiv.innerHTML = `
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                    <span class="text-white font-medium">${participant.name.charAt(0)}</span>
                </div>
                <div>
                    <div class="flex items-center space-x-1">
                        <p class="font-medium text-gray-900">${participant.name}</p>
                        ${participant.is_verified ? '<svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' : ''}
                        ${participant.is_friend ? '<span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Friend</span>' : ''}
                    </div>
                    <p class="text-sm text-gray-500">${participant.status} • ${participant.role}</p>
                </div>
            </div>
            ${participant.role === 'host' || participant.role === 'moderator' ? `
                <div class="flex space-x-1">
                    <button onclick="muteParticipant(${participant.user_id})"
                            class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded hover:bg-yellow-200">
                        ${participant.is_muted ? 'Unmute' : 'Mute'}
                    </button>
                    ${participant.role !== 'host' ? `
                        <button onclick="kickParticipant(${participant.user_id})"
                                class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded hover:bg-red-200">
                            Kick
                        </button>
                    ` : ''}
                </div>
            ` : ''}
        `;

        participantsList.appendChild(participantDiv);
    });
}

// Refresh participants
function refreshParticipants() {
    loadParticipants();
}

// Show invite modal
async function showInviteModal() {
    try {
        const response = await fetch(`/voice-rooms/${roomId}/invitable-friends`);
        const data = await response.json();

        if (data.success) {
            displayInvitableFriends(data.friends);
            document.getElementById('inviteModal').classList.remove('hidden');
        } else {
            alert('Failed to load friends');
        }
    } catch (error) {
        console.error('Error loading friends:', error);
        alert('Failed to load friends');
    }
}

// Hide invite modal
function hideInviteModal() {
    document.getElementById('inviteModal').classList.add('hidden');
    selectedFriends = [];
}

// Display invitable friends
function displayInvitableFriends(friends) {
    const friendsList = document.getElementById('friendsList');
    friendsList.innerHTML = '';

    if (friends.length === 0) {
        friendsList.innerHTML = '<p class="text-gray-500 text-center">No friends available to invite</p>';
        return;
    }

    friends.forEach(friend => {
        const friendDiv = document.createElement('div');
        friendDiv.className = 'flex items-center space-x-3 p-2 hover:bg-gray-50 rounded';

        friendDiv.innerHTML = `
            <input type="checkbox" id="friend_${friend.id}" value="${friend.id}"
                   onchange="toggleFriendSelection(${friend.id})"
                   class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span class="text-white text-sm font-medium">${friend.name.charAt(0)}</span>
            </div>
            <label for="friend_${friend.id}" class="flex-1 cursor-pointer">
                <p class="font-medium text-gray-900">${friend.name}</p>
                <p class="text-sm text-gray-500">@${friend.username}</p>
            </label>
        `;

        friendsList.appendChild(friendDiv);
    });
}

// Toggle friend selection
function toggleFriendSelection(friendId) {
    const checkbox = document.getElementById(`friend_${friendId}`);
    if (checkbox.checked) {
        selectedFriends.push(friendId);
    } else {
        selectedFriends = selectedFriends.filter(id => id !== friendId);
    }
}

// Send invitations
async function sendInvitations() {
    if (selectedFriends.length === 0) {
        alert('Please select at least one friend to invite');
        return;
    }

    try {
        const response = await fetch(`/voice-rooms/${roomId}/invite-friends`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                friend_ids: selectedFriends
            })
        });

        const data = await response.json();

        if (data.success) {
            alert(data.message);
            hideInviteModal();
        } else {
            alert('Failed to send invitations');
        }
    } catch (error) {
        console.error('Error sending invitations:', error);
        alert('Failed to send invitations');
    }
}

// Mute participant (host/moderator only)
async function muteParticipant(userId) {
    try {
        const response = await fetch(`/voice-rooms/${roomId}/mute/${userId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            loadParticipants(); // Refresh participants list
        } else {
            alert(data.message);
        }
    } catch (error) {
        console.error('Error muting participant:', error);
        alert('Failed to mute participant');
    }
}

// Kick participant (host/moderator only)
async function kickParticipant(userId) {
    if (confirm('Are you sure you want to kick this participant?')) {
        try {
            const response = await fetch(`/voice-rooms/${roomId}/kick/${userId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                loadParticipants(); // Refresh participants list
                document.getElementById('participantCount').textContent = data.participants_count;
            } else {
                alert(data.message);
            }
        } catch (error) {
            console.error('Error kicking participant:', error);
            alert('Failed to kick participant');
        }
    }
}

// Chat functionality
let lastMessageId = 0;
let isLoadingMessages = false;

// Initialize chat
function initializeChat() {
    loadMessages();
    setInterval(loadMessages, 3000); // Check for new messages every 3 seconds

    // Character counter
    const chatInput = document.getElementById('chatInput');
    const charCount = document.getElementById('charCount');

    if (chatInput && charCount) {
        chatInput.addEventListener('input', function() {
            charCount.textContent = this.value.length;
        });
    }
}

// Load messages
async function loadMessages() {
    if (isLoadingMessages) return;
    isLoadingMessages = true;

    try {
        const response = await fetch(`/voice-rooms/${roomId}/messages?last_message_id=${lastMessageId}`);
        const data = await response.json();

        if (data.success && data.messages.length > 0) {
            displayMessages(data.messages);
            lastMessageId = data.last_message_id;
        }

        // Hide placeholder if we have messages
        if (lastMessageId > 0) {
            const placeholder = document.getElementById('chatPlaceholder');
            if (placeholder) {
                placeholder.style.display = 'none';
            }
        }
    } catch (error) {
        console.error('Error loading messages:', error);
    } finally {
        isLoadingMessages = false;
    }
}

// Display messages
function displayMessages(messages) {
    const chatMessages = document.getElementById('chatMessages');
    const shouldScrollToBottom = chatMessages.scrollTop + chatMessages.clientHeight >= chatMessages.scrollHeight - 10;

    messages.forEach(message => {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message-item ${message.is_own_message ? 'own-message' : 'other-message'}`;
        messageDiv.setAttribute('data-message-id', message.id);

        let messageContent = '';

        if (message.message_type === 'system') {
            messageContent = `
                <div class="text-center text-sm text-gray-500 italic py-2">
                    ${message.message}
                </div>
            `;
        } else {
            messageContent = `
                <div class="flex items-start space-x-2 ${message.is_own_message ? 'flex-row-reverse space-x-reverse' : ''}">
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <span class="text-white text-sm font-medium">${message.sender_name.charAt(0)}</span>
                    </div>
                    <div class="flex-1 ${message.is_own_message ? 'text-right' : ''}">
                        <div class="flex items-center space-x-1 ${message.is_own_message ? 'justify-end' : ''}">
                            <span class="font-medium text-sm text-gray-900">${message.sender_name}</span>
                            ${message.sender_verified ? '<svg class="w-3 h-3 text-blue-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' : ''}
                            <span class="text-xs text-gray-500">${message.created_at}</span>
                        </div>
                        <div class="mt-1">
                            <div class="inline-block px-3 py-2 rounded-lg ${message.is_own_message ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-900'} max-w-xs break-words">
                                ${message.message}
                            </div>
                        </div>
                        ${(message.can_edit || message.can_delete) ? `
                            <div class="mt-1 flex space-x-2 text-xs ${message.is_own_message ? 'justify-end' : ''}">
                                ${message.can_edit ? `<button onclick="editMessage(${message.id})" class="text-blue-500 hover:text-blue-700">Edit</button>` : ''}
                                ${message.can_delete ? `<button onclick="deleteMessage(${message.id})" class="text-red-500 hover:text-red-700">Delete</button>` : ''}
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        messageDiv.innerHTML = messageContent;
        chatMessages.appendChild(messageDiv);
    });

    // Auto-scroll to bottom if user was already at bottom
    if (shouldScrollToBottom) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

// Send message
async function sendMessage() {
    const chatInput = document.getElementById('chatInput');
    const anonymousMode = document.getElementById('anonymousMode');
    const message = chatInput.value.trim();

    if (!message) return;

    try {
        const response = await fetch(`/voice-rooms/${roomId}/messages`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                message: message,
                is_anonymous: anonymousMode ? anonymousMode.checked : false
            })
        });

        const data = await response.json();

        if (data.success) {
            chatInput.value = '';
            document.getElementById('charCount').textContent = '0';

            // Add the message immediately to the chat
            displayMessages([data.message_data]);
            lastMessageId = Math.max(lastMessageId, data.message_data.id);
        } else {
            alert(data.message);
        }
    } catch (error) {
        console.error('Error sending message:', error);
        alert('Failed to send message');
    }
}

// Refresh messages
function refreshMessages() {
    lastMessageId = 0;
    document.getElementById('chatMessages').innerHTML = '<div id="chatPlaceholder" class="text-gray-500 text-center">Loading messages...</div>';
    loadMessages();
}

// Delete message
async function deleteMessage(messageId) {
    if (!confirm('Are you sure you want to delete this message?')) return;

    try {
        const response = await fetch(`/voice-rooms/${roomId}/messages/${messageId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            // Remove the message from display
            const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
            if (messageElement) {
                messageElement.remove();
            }
        } else {
            alert(data.message);
        }
    } catch (error) {
        console.error('Error deleting message:', error);
        alert('Failed to delete message');
    }
}

// Edit message (placeholder for future implementation)
function editMessage(messageId) {
    alert('Message editing will be implemented in a future update');
}

// Handle Enter key in chat input
document.getElementById('chatInput')?.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        sendMessage();
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop());
    }
});
</script>
@endsection