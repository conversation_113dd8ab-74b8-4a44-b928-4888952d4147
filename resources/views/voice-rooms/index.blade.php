@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <div class="flex justify-between items-center">
        <h1 class="text-3xl font-bold text-gray-900">Voice Rooms</h1>
        @auth
            <a href="{{ route('voice-rooms.create') }}" 
               class="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600">
                Create New Room
            </a>
        @endauth
    </div>

    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            <p class="text-blue-800">
                <strong>Voice Rooms:</strong> Join live voice conversations with other users. No video, just voice chat!
            </p>
        </div>
    </div>

    @forelse($rooms as $room)
        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
            <div class="flex justify-between items-start mb-4">
                <div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ $room->name }}</h3>
                    <p class="text-gray-600 mb-3">{{ $room->description }}</p>
                    
                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                            </svg>
                            Host: {{ $room->host->name }}
                        </div>
                        
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                            </svg>
                            {{ $room->current_participants }}/{{ $room->max_participants }} participants
                        </div>
                        
                        @if($room->category)
                            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">
                                {{ $room->category }}
                            </span>
                        @endif
                        
                        @if($room->is_private)
                            <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-xs">
                                Private
                            </span>
                        @endif
                    </div>
                </div>
                
                <div class="flex items-center space-x-2">
                    <span class="flex items-center text-green-600">
                        <span class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
                        Live
                    </span>
                </div>
            </div>

            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-500">
                    Created {{ $room->created_at->diffForHumans() }}
                </span>
                
                <div class="flex items-center space-x-2">
                    <a href="{{ route('voice-rooms.show', $room) }}" 
                       class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 text-sm">
                        Join Room
                    </a>
                    
                    @auth
                        @if($room->host_id === auth()->id())
                            <a href="{{ route('voice-rooms.edit', $room) }}" 
                               class="text-green-600 hover:text-green-800 text-sm">
                                Edit
                            </a>
                            <form method="POST" action="{{ route('voice-rooms.destroy', $room) }}" 
                                  class="inline" onsubmit="return confirm('Are you sure you want to end this room?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="text-red-600 hover:text-red-800 text-sm">
                                    End Room
                                </button>
                            </form>
                        @endif
                    @endauth
                </div>
            </div>
        </div>
    @empty
        <div class="text-center py-12">
            <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
            </svg>
            <p class="text-gray-500 text-lg mb-4">No active voice rooms right now.</p>
            @auth
                <a href="{{ route('voice-rooms.create') }}" 
                   class="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600">
                    Create the First Room
                </a>
            @else
                <p class="text-gray-400">Login to create a voice room</p>
            @endauth
        </div>
    @endforelse

    {{ $rooms->links() }}
</div>
@endsection