<div class="bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden group">
    <!-- Card Header -->
    <div class="p-6 pb-4">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-semibold">
                    @if($confession->is_anonymous)
                        <span>A</span>
                    @else
                        <span>{{ substr($confession->user->name, 0, 1) }}</span>
                    @endif
                </div>
                <div class="ml-3">
                    <p class="font-medium text-gray-900">
                        @if($confession->is_anonymous)
                            Anonymous
                        @else
                            {{ $confession->user->name }}
                        @endif
                    </p>
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <span>{{ $confession->created_at->diffForHumans() }}</span>
                        @if($confession->category)
                            <span>•</span>
                            <span class="bg-gray-100 px-2 py-1 rounded-full text-xs">
                                {{ ucfirst($confession->category) }}
                            </span>
                        @endif
                        @if($confession->is_ai_generated)
                            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs">
                                🤖 AI
                            </span>
                        @endif
                        @if($confession->is_nsfw)
                            <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">
                                🔞 NSFW
                            </span>
                        @endif
                    </div>
                </div>
            </div>
            
            <!-- Dropdown Menu -->
            <div class="relative">
                <button class="text-gray-400 hover:text-gray-600 p-2" onclick="toggleDropdown({{ $confession->id }})">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                    </svg>
                </button>
                <div id="dropdown-{{ $confession->id }}" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border">
                    <div class="py-1">
                        @auth
                            <button onclick="shareConfession({{ $confession->id }})" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                📤 Share
                            </button>
                            <button onclick="toggleBookmark({{ $confession->id }})" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                🔖 {{ Auth::user()->hasBookmarked($confession) ? 'Remove Bookmark' : 'Bookmark' }}
                            </button>
                            @if($confession->user_id !== Auth::id())
                                <button onclick="reportConfession({{ $confession->id }})" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                    🚨 Report
                                </button>
                            @endif
                        @else
                            <a href="{{ route('login') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                Login to interact
                            </a>
                        @endauth
                    </div>
                </div>
            </div>
        </div>

        <!-- Confession Content -->
        <div class="mb-4">
            @if($confession->image)
                <img src="{{ asset('storage/' . $confession->image) }}" 
                     alt="Confession image" 
                     class="w-full h-48 object-cover rounded-lg mb-4">
            @endif
            
            <div class="confession-content">
                @if(strlen($confession->content) > 300)
                    <p class="text-gray-800 leading-relaxed">
                        <span id="content-preview-{{ $confession->id }}">
                            {{ substr($confession->content, 0, 300) }}...
                        </span>
                        <span id="content-full-{{ $confession->id }}" class="hidden">
                            {{ $confession->content }}
                        </span>
                        <button onclick="toggleContent({{ $confession->id }})" 
                                class="text-purple-600 hover:text-purple-800 font-medium ml-2">
                            <span id="toggle-text-{{ $confession->id }}">Read more</span>
                        </button>
                    </p>
                @else
                    <p class="text-gray-800 leading-relaxed">{{ $confession->content }}</p>
                @endif
            </div>
            
            <!-- Tags -->
            @if($confession->tags && count($confession->tags) > 0)
                <div class="flex flex-wrap gap-2 mt-3">
                    @foreach($confession->tags as $tag)
                        <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs">
                            #{{ $tag }}
                        </span>
                    @endforeach
                </div>
            @endif
        </div>

        <!-- Engagement Stats -->
        <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
            <div class="flex items-center space-x-4">
                <span class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                    </svg>
                    {{ number_format($confession->views) }}
                </span>
                <span class="flex items-center">
                    ❤️ {{ number_format($confession->likes_count) }}
                </span>
                <span class="flex items-center">
                    🔖 {{ number_format($confession->bookmark_count) }}
                </span>
                <span class="flex items-center">
                    📤 {{ number_format($confession->share_count) }}
                </span>
            </div>
            <div class="text-xs">
                Engagement: {{ number_format($confession->getEngagementScore()) }}
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center justify-between pt-4 border-t border-gray-100">
            @auth
                <!-- Like Button -->
                <button onclick="toggleLike({{ $confession->id }})" 
                        class="flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors {{ Auth::user()->likes()->where('confession_id', $confession->id)->exists() ? 'bg-red-100 text-red-600' : 'hover:bg-gray-100 text-gray-600' }}">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"/>
                    </svg>
                    <span>Like</span>
                </button>

                <!-- Comment Button -->
                <button onclick="showComments({{ $confession->id }})" 
                        class="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 text-gray-600 transition-colors">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"/>
                    </svg>
                    <span>Comment</span>
                </button>

                <!-- Share Button -->
                <button onclick="shareConfession({{ $confession->id }})" 
                        class="flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-gray-100 text-gray-600 transition-colors">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"/>
                    </svg>
                    <span>Share</span>
                </button>
            @else
                <div class="flex items-center space-x-4 text-gray-500">
                    <span class="flex items-center">
                        <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"/>
                        </svg>
                        Like
                    </span>
                    <span class="flex items-center">
                        <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"/>
                        </svg>
                        Comment
                    </span>
                    <a href="{{ route('login') }}" class="text-purple-600 hover:text-purple-800 text-sm">
                        Login to interact
                    </a>
                </div>
            @endauth
        </div>
    </div>
</div>

<script>
function toggleDropdown(confessionId) {
    const dropdown = document.getElementById(`dropdown-${confessionId}`);
    dropdown.classList.toggle('hidden');
    
    // Close other dropdowns
    document.querySelectorAll('[id^="dropdown-"]').forEach(el => {
        if (el.id !== `dropdown-${confessionId}`) {
            el.classList.add('hidden');
        }
    });
}

function toggleContent(confessionId) {
    const preview = document.getElementById(`content-preview-${confessionId}`);
    const full = document.getElementById(`content-full-${confessionId}`);
    const toggleText = document.getElementById(`toggle-text-${confessionId}`);
    
    if (preview.classList.contains('hidden')) {
        preview.classList.remove('hidden');
        full.classList.add('hidden');
        toggleText.textContent = 'Read more';
    } else {
        preview.classList.add('hidden');
        full.classList.remove('hidden');
        toggleText.textContent = 'Read less';
    }
}

async function toggleLike(confessionId) {
    try {
        const response = await fetch(`/confessions/${confessionId}/like`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            location.reload(); // Simple reload for now
        }
    } catch (error) {
        console.error('Error toggling like:', error);
    }
}

async function toggleBookmark(confessionId) {
    try {
        const response = await fetch(`/confessions/${confessionId}/bookmark`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            location.reload(); // Simple reload for now
        }
    } catch (error) {
        console.error('Error toggling bookmark:', error);
    }
}

function shareConfession(confessionId) {
    if (navigator.share) {
        navigator.share({
            title: 'Check out this confession',
            url: window.location.origin + `/confessions/${confessionId}`
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.origin + `/confessions/${confessionId}`);
        alert('Link copied to clipboard!');
    }
}

function showComments(confessionId) {
    // Navigate to confession detail page
    window.location.href = `/confessions/${confessionId}`;
}

function reportConfession(confessionId) {
    if (confirm('Are you sure you want to report this confession?')) {
        // Implement reporting functionality
        alert('Thank you for your report. We will review this content.');
    }
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('[onclick*="toggleDropdown"]')) {
        document.querySelectorAll('[id^="dropdown-"]').forEach(el => {
            el.classList.add('hidden');
        });
    }
});
</script>
