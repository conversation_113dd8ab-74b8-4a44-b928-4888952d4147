@extends('layouts.app')

@section('content')
<div class="max-w-3xl mx-auto">
    <div class="bg-white rounded-lg shadow-md p-8">
        <div class="flex justify-between items-start mb-6">
            <div class="flex items-center space-x-3">
                @if($confession->is_anonymous)
                    <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900">Anonymous</h3>
                        <p class="text-sm text-gray-500">Posted {{ $confession->created_at->diffForHumans() }}</p>
                    </div>
                @else
                    <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                        <span class="text-white font-medium">
                            {{ substr($confession->user->name, 0, 1) }}
                        </span>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900">{{ $confession->user->name }}</h3>
                        <p class="text-sm text-gray-500">Posted {{ $confession->created_at->diffForHumans() }}</p>
                    </div>
                @endif
                
                @if($confession->category)
                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                        {{ $confession->category }}
                    </span>
                @endif
            </div>
            
            @auth
                @if($confession->user_id === auth()->id())
                    <div class="flex items-center space-x-2">
                        <a href="{{ route('confessions.edit', $confession) }}" 
                           class="text-green-600 hover:text-green-800 text-sm">
                            Edit
                        </a>
                        <form method="POST" action="{{ route('confessions.destroy', $confession) }}" 
                              class="inline" onsubmit="return confirm('Are you sure you want to delete this confession?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="text-red-600 hover:text-red-800 text-sm">
                                Delete
                            </button>
                        </form>
                    </div>
                @endif
            @endauth
        </div>

        <div class="prose max-w-none mb-6">
            <p class="text-gray-800 text-lg leading-relaxed">{{ $confession->content }}</p>
        </div>

        <div class="flex items-center justify-between pt-4 border-t border-gray-200">
            <div class="flex items-center space-x-6">
                @auth
                    <button onclick="toggleLike({{ $confession->id }})" 
                            id="like-btn-{{ $confession->id }}"
                            class="flex items-center space-x-2 {{ $confession->isLikedBy(auth()->user()) ? 'text-red-500' : 'text-gray-500' }} hover:text-red-500 transition-colors">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                        </svg>
                        <span id="like-count-{{ $confession->id }}">{{ $confession->likes_count }}</span>
                        <span>{{ $confession->likes_count == 1 ? 'Like' : 'Likes' }}</span>
                    </button>
                @else
                    <div class="flex items-center space-x-2 text-gray-500">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                        </svg>
                        <span>{{ $confession->likes_count }}</span>
                        <span>{{ $confession->likes_count == 1 ? 'Like' : 'Likes' }}</span>
                    </div>
                @endauth

                <div class="flex items-center space-x-2 text-gray-500">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    <span>{{ $confession->views }}</span>
                    <span>{{ $confession->views == 1 ? 'View' : 'Views' }}</span>
                </div>
            </div>

            <a href="{{ route('confessions.index') }}" 
               class="text-blue-600 hover:text-blue-800 font-medium">
                ← Back to Confessions
            </a>
        </div>
    </div>

    <!-- Comments Section -->
    <div class="bg-white rounded-lg shadow-md p-6 mt-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
            Comments ({{ $confession->comments->count() }})
        </h3>

        <!-- Add Comment Form -->
        @auth
            <form method="POST" action="{{ route('comments.store', $confession) }}" class="mb-6">
                @csrf
                <div class="mb-4">
                    <textarea name="content" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Share your thoughts..." required>{{ old('content') }}</textarea>
                    @error('content')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_anonymous" value="1" 
                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                               {{ old('is_anonymous') ? 'checked' : '' }}>
                        <span class="ml-2 text-sm text-gray-700">Comment anonymously</span>
                    </label>
                    
                    <button type="submit" 
                            class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        Add Comment
                    </button>
                </div>
            </form>
        @else
            <div class="mb-6 p-4 bg-gray-50 rounded-lg text-center">
                <p class="text-gray-600">Please <a href="{{ route('login') }}" class="text-blue-600 hover:text-blue-800">login</a> to add comments.</p>
            </div>
        @endauth

        <!-- Comments List -->
        <div class="space-y-4">
            @forelse($confession->comments->sortByDesc('created_at') as $comment)
                <div class="border-b border-gray-200 pb-4 last:border-b-0">
                    <div class="flex justify-between items-start mb-2">
                        <div class="flex items-center space-x-2">
                            @if($comment->is_anonymous)
                                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <span class="font-medium text-gray-700">Anonymous</span>
                            @else
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm font-medium">
                                        {{ substr($comment->user->name, 0, 1) }}
                                    </span>
                                </div>
                                <span class="font-medium text-gray-700">{{ $comment->user->name }}</span>
                            @endif
                            
                            <span class="text-sm text-gray-500">{{ $comment->created_at->diffForHumans() }}</span>
                        </div>
                        
                        @auth
                            @if($comment->user_id === auth()->id())
                                <form method="POST" action="{{ route('comments.destroy', $comment) }}" 
                                      class="inline" onsubmit="return confirm('Are you sure you want to delete this comment?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-500 hover:text-red-700 text-sm">
                                        Delete
                                    </button>
                                </form>
                            @endif
                        @endauth
                    </div>
                    
                    <p class="text-gray-800 ml-10">{{ $comment->content }}</p>
                </div>
            @empty
                <p class="text-gray-500 text-center py-4">No comments yet. Be the first to share your thoughts!</p>
            @endforelse
        </div>
    </div>
</div>
@endsection