@extends('layouts.app')

@section('content')
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Confessions Section (Left Side) -->
    <div class="lg:col-span-2 space-y-6">
        <div class="flex justify-between items-center">
            <h1 class="text-3xl font-bold text-gray-900">Recent Confessions</h1>
            @auth
                <a href="{{ route('confessions.create') }}" 
                   class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600">
                    Share Your Confession
                </a>
            @endauth
        </div>

    @forelse($confessions as $confession)
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-start mb-4">
                <div class="flex items-center space-x-2">
                    @if($confession->is_anonymous)
                        <span class="text-gray-500 font-medium">Anonymous</span>
                    @else
                        <a href="{{ route('users.profile', $confession->user) }}" class="text-gray-700 font-medium hover:text-blue-600">
                            {{ $confession->user->name }}
                        </a>
                    @endif
                    
                    @if($confession->category)
                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                            {{ $confession->category }}
                        </span>
                    @endif
                </div>
                
                <span class="text-gray-500 text-sm">
                    {{ $confession->created_at->diffForHumans() }}
                </span>
            </div>

            <p class="text-gray-800 mb-4 leading-relaxed">{{ $confession->content }}</p>
            
            @if($confession->image)
                <div class="mb-4">
                    <img src="{{ asset('storage/' . $confession->image) }}" 
                         alt="Confession image" 
                         class="max-w-full h-auto rounded-lg shadow-sm">
                </div>
            @endif

            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    @auth
                        <!-- Reaction Buttons -->
                        @php
                            $reactions = $confession->getReactionCounts();
                            $userReaction = $confession->getUserReaction(auth()->user());
                            $reactionEmojis = [
                                'heart' => '❤️',
                                'support' => '🤗', 
                                'sad' => '😢',
                                'angry' => '😠',
                                'laugh' => '😂'
                            ];
                        @endphp
                        
                        <div class="flex items-center space-x-2" id="reactions-{{ $confession->id }}">
                            @foreach($reactionEmojis as $type => $emoji)
                                <button onclick="toggleReaction({{ $confession->id }}, '{{ $type }}')" 
                                        class="flex items-center space-x-1 px-2 py-1 rounded-full text-sm transition-colors {{ $userReaction && $userReaction->type === $type ? 'bg-blue-100 text-blue-600' : 'text-gray-500 hover:bg-gray-100' }}">
                                    <span>{{ $emoji }}</span>
                                    <span class="reaction-count-{{ $type }}">{{ $reactions[$type] ?? 0 }}</span>
                                </button>
                            @endforeach
                        </div>
                        
                        <!-- Legacy Like Button -->
                        <button onclick="toggleLike({{ $confession->id }})" 
                                id="like-btn-{{ $confession->id }}"
                                class="flex items-center space-x-1 {{ $confession->isLikedBy(auth()->user()) ? 'text-red-500' : 'text-gray-500' }} hover:text-red-500">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                            </svg>
                            <span id="like-count-{{ $confession->id }}">{{ $confession->likes_count }}</span>
                        </button>
                    @else
                        <!-- Show reactions for non-authenticated users -->
                        @php $reactions = $confession->getReactionCounts(); @endphp
                        <div class="flex items-center space-x-2">
                            @foreach(['heart' => '❤️', 'support' => '🤗', 'sad' => '😢', 'angry' => '😠', 'laugh' => '😂'] as $type => $emoji)
                                @if(($reactions[$type] ?? 0) > 0)
                                    <div class="flex items-center space-x-1 text-gray-500">
                                        <span>{{ $emoji }}</span>
                                        <span>{{ $reactions[$type] }}</span>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                        
                        <div class="flex items-center space-x-1 text-gray-500">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                            </svg>
                            <span>{{ $confession->likes_count }}</span>
                        </div>
                    @endauth

                    <div class="flex items-center space-x-1 text-gray-500">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        <span>{{ $confession->views }}</span>
                    </div>
                    
                    <div class="flex items-center space-x-1 text-gray-500">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        <span>{{ $confession->comments->count() }}</span>
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    <a href="{{ route('confessions.show', $confession) }}" 
                       class="text-blue-500 hover:text-blue-700 text-sm">
                        Read More
                    </a>
                    
                    @auth
                        @if($confession->user_id === auth()->id())
                            <a href="{{ route('confessions.edit', $confession) }}" 
                               class="text-green-500 hover:text-green-700 text-sm">
                                Edit
                            </a>
                            <form method="POST" action="{{ route('confessions.destroy', $confession) }}" 
                                  class="inline" onsubmit="return confirm('Are you sure?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="text-red-500 hover:text-red-700 text-sm">
                                    Delete
                                </button>
                            </form>
                        @endif
                    @endauth
                </div>
            </div>
        </div>
    @empty
        <div class="text-center py-12">
            <p class="text-gray-500 text-lg">No confessions yet. Be the first to share!</p>
            @auth
                <a href="{{ route('confessions.create') }}" 
                   class="mt-4 inline-block bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600">
                    Share Your Confession
                </a>
            @endauth
        </div>
    @endforelse

        {{ $confessions->links() }}
    </div>

    <!-- Categories & Stats Sidebar (Right Side) -->
    <div class="space-y-6">
        <!-- Quick Stats -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Community Stats</h2>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600">Total Confessions</span>
                    <span class="font-semibold text-blue-600">{{ \App\Models\Confession::count() }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Active Voice Rooms</span>
                    <span class="font-semibold text-green-600">{{ \App\Models\VoiceRoom::where('is_active', true)->count() }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Total Likes</span>
                    <span class="font-semibold text-red-600">{{ \App\Models\Like::count() }}</span>
                </div>
            </div>
        </div>

        <!-- Categories -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Browse by Category</h2>
            @php
                $categories = \App\Models\Confession::select('category')
                    ->whereNotNull('category')
                    ->groupBy('category')
                    ->get()
                    ->map(function($item) {
                        return [
                            'name' => $item->category,
                            'count' => \App\Models\Confession::where('category', $item->category)->count()
                        ];
                    });
            @endphp
            
            <div class="space-y-2">
                <a href="{{ route('home') }}" class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="flex items-center">
                        <span class="w-3 h-3 bg-gray-400 rounded-full mr-3"></span>
                        <span class="text-gray-700">All Categories</span>
                    </div>
                    <span class="text-sm text-gray-500">{{ \App\Models\Confession::count() }}</span>
                </a>
                
                @foreach(['Love' => 'bg-red-400', 'Work' => 'bg-blue-400', 'Family' => 'bg-green-400', 'Friends' => 'bg-yellow-400', 'Personal' => 'bg-purple-400', 'Other' => 'bg-gray-400'] as $category => $color)
                    @php $count = \App\Models\Confession::where('category', $category)->count(); @endphp
                    @if($count > 0)
                        <a href="{{ route('home', ['category' => $category]) }}" class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <div class="flex items-center">
                                <span class="w-3 h-3 {{ $color }} rounded-full mr-3"></span>
                                <span class="text-gray-700">{{ $category }}</span>
                            </div>
                            <span class="text-sm text-gray-500">{{ $count }}</span>
                        </a>
                    @endif
                @endforeach
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
            <div class="space-y-3">
                @auth
                    <a href="{{ route('confessions.create') }}" class="flex items-center p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                        <svg class="w-5 h-5 text-blue-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-blue-700 font-medium">New Confession</span>
                    </a>
                    
                    <a href="{{ route('voice-rooms.create') }}" class="flex items-center p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                        <svg class="w-5 h-5 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                        </svg>
                        <span class="text-green-700 font-medium">Create Voice Room</span>
                    </a>
                    
                    <a href="{{ route('confessions.random') }}" class="flex items-center p-3 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                        <svg class="w-5 h-5 text-purple-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-purple-700 font-medium">Random Confession</span>
                    </a>
                @else
                    <div class="text-center p-4 bg-gray-50 rounded-lg">
                        <p class="text-gray-600 mb-3">Join our community!</p>
                        <div class="space-y-2">
                            <a href="{{ route('register') }}" class="block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Sign Up</a>
                            <a href="{{ route('login') }}" class="block text-blue-500 hover:text-blue-700">Login</a>
                            <a href="{{ route('confessions.random') }}" class="block text-purple-500 hover:text-purple-700 text-center mt-2">Random Confession</a>
                        </div>
                    </div>
                @endauth
            </div>
        </div>

        <!-- User Badges -->
        @auth
            @if(auth()->user()->badges->count() > 0)
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Your Badges</h2>
                    <div class="grid grid-cols-2 gap-2">
                        @foreach(auth()->user()->badges as $badge)
                            <div class="flex items-center p-2 {{ $badge->color }} bg-opacity-10 rounded-lg" title="{{ $badge->description }}">
                                <span class="text-lg mr-2">{{ $badge->icon }}</span>
                                <span class="text-xs font-medium text-gray-700">{{ $badge->name }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        @endauth
        
        <!-- Trending Topics -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Trending Now</h2>
            <div class="space-y-2">
                @php
                    $trending = \App\Models\Confession::where('created_at', '>=', now()->subDays(7))
                        ->selectRaw('*, (likes_count * 2 + views + (SELECT COUNT(*) FROM comments WHERE confession_id = confessions.id) * 3) as trending_score')
                        ->orderBy('trending_score', 'desc')
                        ->limit(3)
                        ->get();
                @endphp
                
                @forelse($trending as $trend)
                    <a href="{{ route('confessions.show', $trend) }}" class="block p-3 rounded-lg hover:bg-gray-50 transition-colors">
                        <p class="text-sm text-gray-700 line-clamp-2">{{ Str::limit($trend->content, 60) }}</p>
                        <div class="flex items-center mt-2 text-xs text-gray-500">
                            <span class="mr-3">❤️ {{ $trend->likes_count }}</span>
                            <span>👁️ {{ $trend->views }}</span>
                        </div>
                    </a>
                @empty
                    <p class="text-gray-500 text-sm">No trending confessions yet</p>
                @endforelse
            </div>
        </div>
    </div>
</div>
@endsection