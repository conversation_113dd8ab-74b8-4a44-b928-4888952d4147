@extends('layouts.app')

@section('content')
<div class="max-w-2xl mx-auto">
    <h1 class="text-3xl font-bold text-gray-900 mb-6">Edit Your Confession</h1>

    <div class="bg-white rounded-lg shadow-md p-6">
        <form method="POST" action="{{ route('confessions.update', $confession) }}">
            @csrf
            @method('PUT')
            
            <div class="mb-6">
                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                    Your Confession
                </label>
                <textarea name="content" id="content" rows="6" 
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Share what's on your mind..." required>{{ old('content', $confession->content) }}</textarea>
                @error('content')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                    Category (Optional)
                </label>
                <select name="category" id="category" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="">Select a category</option>
                    <option value="Love" {{ old('category', $confession->category) == 'Love' ? 'selected' : '' }}>Love</option>
                    <option value="Work" {{ old('category', $confession->category) == 'Work' ? 'selected' : '' }}>Work</option>
                    <option value="Family" {{ old('category', $confession->category) == 'Family' ? 'selected' : '' }}>Family</option>
                    <option value="Friends" {{ old('category', $confession->category) == 'Friends' ? 'selected' : '' }}>Friends</option>
                    <option value="Personal" {{ old('category', $confession->category) == 'Personal' ? 'selected' : '' }}>Personal</option>
                    <option value="Other" {{ old('category', $confession->category) == 'Other' ? 'selected' : '' }}>Other</option>
                </select>
                @error('category')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            @if($confession->is_anonymous)
                <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p class="text-sm text-yellow-800">
                        <strong>Note:</strong> This confession was posted anonymously and will remain anonymous.
                    </p>
                </div>
            @endif

            <div class="flex items-center justify-between">
                <a href="{{ route('confessions.show', $confession) }}" 
                   class="text-gray-600 hover:text-gray-800">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    Update Confession
                </button>
            </div>
        </form>
    </div>
</div>
@endsection