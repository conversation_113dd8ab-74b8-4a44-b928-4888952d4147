@extends('layouts.app')

@section('content')
<div class="max-w-2xl mx-auto">
    <h1 class="text-3xl font-bold text-gray-900 mb-6">Share Your Confession</h1>

    <div class="bg-white rounded-lg shadow-md p-6">
        <form method="POST" action="{{ route('confessions.store') }}" enctype="multipart/form-data">
            @csrf
            
            <div class="mb-6">
                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                    Your Confession
                </label>
                <textarea name="content" id="content" rows="6" 
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Share what's on your mind..." required>{{ old('content') }}</textarea>
                @error('content')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                    Category (Optional)
                </label>
                <select name="category" id="category" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="">Select a category</option>
                    <option value="Love" {{ old('category') == 'Love' ? 'selected' : '' }}>Love</option>
                    <option value="Work" {{ old('category') == 'Work' ? 'selected' : '' }}>Work</option>
                    <option value="Family" {{ old('category') == 'Family' ? 'selected' : '' }}>Family</option>
                    <option value="Friends" {{ old('category') == 'Friends' ? 'selected' : '' }}>Friends</option>
                    <option value="Personal" {{ old('category') == 'Personal' ? 'selected' : '' }}>Personal</option>
                    <option value="Other" {{ old('category') == 'Other' ? 'selected' : '' }}>Other</option>
                </select>
                @error('category')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label for="image" class="block text-sm font-medium text-gray-700 mb-2">
                    Image (Optional)
                </label>
                <input type="file" name="image" id="image" accept="image/*"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <p class="text-xs text-gray-500 mt-1">
                    Upload an image to accompany your confession (max 2MB)
                </p>
                @error('image')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label class="flex items-center">
                    <input type="checkbox" name="is_anonymous" value="1" 
                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                           {{ old('is_anonymous') ? 'checked' : '' }}>
                    <span class="ml-2 text-sm text-gray-700">Post anonymously</span>
                </label>
                <p class="text-xs text-gray-500 mt-1">
                    When checked, your name won't be shown with this confession
                </p>
            </div>

            <div class="flex items-center justify-between">
                <a href="{{ route('confessions.index') }}" 
                   class="text-gray-600 hover:text-gray-800">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    Share Confession
                </button>
            </div>
        </form>
    </div>
</div>
@endsection