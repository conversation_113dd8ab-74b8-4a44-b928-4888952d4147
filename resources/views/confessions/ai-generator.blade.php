@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="flex items-center mb-6">
            <div class="bg-gradient-to-r from-purple-500 to-pink-500 p-3 rounded-lg mr-4">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-gray-900">AI Confession Generator</h1>
                <p class="text-gray-600">Create personalized confessions with AI assistance</p>
            </div>
        </div>

        <form id="ai-generator-form" class="space-y-6">
            @csrf
            
            <!-- Tone Selection -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">Confession Tone</label>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                    @foreach(['wild' => '🔥 Wild & Spicy', 'romantic' => '💕 Romantic', 'dramatic' => '🎭 Dramatic', 'funny' => '😂 Funny', 'dark' => '🌙 Dark & Mysterious', 'mysterious' => '🔮 Mysterious'] as $value => $label)
                        <label class="relative">
                            <input type="radio" name="tone" value="{{ $value }}" class="sr-only peer" {{ $value === 'wild' ? 'checked' : '' }}>
                            <div class="p-3 border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-purple-500 peer-checked:bg-purple-50 hover:border-purple-300 transition-colors">
                                <span class="text-sm font-medium">{{ $label }}</span>
                            </div>
                        </label>
                    @endforeach
                </div>
            </div>

            <!-- Category -->
            <div>
                <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select name="category" id="category" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-purple-500 focus:border-purple-500">
                    <option value="">Select a category...</option>
                    <option value="office">Office/Work</option>
                    <option value="college">College/School</option>
                    <option value="travel">Travel</option>
                    <option value="family">Family</option>
                    <option value="relationship">Relationship</option>
                    <option value="hookup">Hookup</option>
                    <option value="fantasy">Fantasy</option>
                    <option value="cheating">Cheating</option>
                    <option value="first-time">First Time</option>
                    <option value="secret">Secret</option>
                </select>
            </div>

            <!-- Content Type -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">Content Length</label>
                <div class="flex space-x-4">
                    <label class="flex items-center">
                        <input type="radio" name="content_type" value="short" class="text-purple-600 focus:ring-purple-500">
                        <span class="ml-2">Short (Tweet-style, 100-200 words)</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="content_type" value="long" class="text-purple-600 focus:ring-purple-500" checked>
                        <span class="ml-2">Long (Detailed, 300-500 words)</span>
                    </label>
                </div>
            </div>

            <!-- NSFW Toggle -->
            <div class="flex items-center justify-between p-4 bg-red-50 rounded-lg border border-red-200">
                <div>
                    <h3 class="font-medium text-red-900">NSFW Content</h3>
                    <p class="text-sm text-red-700">Allow adult themes and explicit content</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" name="is_nsfw" class="sr-only peer" checked>
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                </label>
            </div>

            <!-- Custom Prompt -->
            <div>
                <label for="custom_prompt" class="block text-sm font-medium text-gray-700 mb-2">Custom Instructions (Optional)</label>
                <textarea name="custom_prompt" id="custom_prompt" rows="3" 
                    class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-purple-500 focus:border-purple-500"
                    placeholder="Add specific details, scenarios, or style preferences..."></textarea>
            </div>

            <!-- Sample Confessions -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Sample Confessions (Optional)</label>
                <p class="text-sm text-gray-600 mb-3">Provide examples of confessions you like to help AI match your preferred style</p>
                <div id="sample-confessions">
                    <textarea name="sample_confessions[]" rows="2" 
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-purple-500 focus:border-purple-500 mb-2"
                        placeholder="Example confession 1..."></textarea>
                </div>
                <button type="button" id="add-sample" class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                    + Add another sample
                </button>
            </div>

            <!-- Save Options -->
            <div class="flex items-center space-x-4">
                <label class="flex items-center">
                    <input type="checkbox" name="save_confession" class="text-purple-600 focus:ring-purple-500">
                    <span class="ml-2">Save generated confession to my profile</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" name="is_anonymous" class="text-purple-600 focus:ring-purple-500" checked>
                    <span class="ml-2">Post anonymously</span>
                </label>
            </div>

            <!-- Generate Button -->
            <div class="flex justify-center">
                <button type="submit" id="generate-btn" 
                    class="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-3 rounded-lg font-medium hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-4 focus:ring-purple-300 transition-all">
                    <span class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                        </svg>
                        Generate Confession
                    </span>
                </button>
            </div>
        </form>

        <!-- Generated Content -->
        <div id="generated-content" class="hidden mt-8 p-6 bg-gray-50 rounded-lg">
            <h3 class="text-lg font-semibold mb-4">Generated Confession</h3>
            <div id="confession-text" class="prose max-w-none"></div>
            <div class="mt-4 flex space-x-3">
                <button id="regenerate-btn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    Regenerate
                </button>
                <button id="copy-btn" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                    Copy Text
                </button>
                <button id="share-btn" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700">
                    Share
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('ai-generator-form');
    const generateBtn = document.getElementById('generate-btn');
    const generatedContent = document.getElementById('generated-content');
    const confessionText = document.getElementById('confession-text');
    
    // Add sample confession functionality
    document.getElementById('add-sample').addEventListener('click', function() {
        const container = document.getElementById('sample-confessions');
        const textarea = document.createElement('textarea');
        textarea.name = 'sample_confessions[]';
        textarea.rows = 2;
        textarea.className = 'w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-purple-500 focus:border-purple-500 mb-2';
        textarea.placeholder = 'Example confession...';
        container.appendChild(textarea);
    });

    // Form submission
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<span class="flex items-center"><svg class="animate-spin w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20"><path d="M4 2a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4z"/></svg>Generating...</span>';
        
        try {
            const formData = new FormData(form);
            const response = await fetch('{{ route("confessions.generate") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('[name="_token"]').value
                }
            });
            
            const data = await response.json();
            
            if (data.confession) {
                confessionText.innerHTML = data.confession.replace(/\n/g, '<br>');
                generatedContent.classList.remove('hidden');
                generatedContent.scrollIntoView({ behavior: 'smooth' });
            } else {
                alert('Error: ' + (data.error || 'Failed to generate confession'));
            }
        } catch (error) {
            alert('Error: ' + error.message);
        } finally {
            generateBtn.disabled = false;
            generateBtn.innerHTML = '<span class="flex items-center"><svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20"><path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/></svg>Generate Confession</span>';
        }
    });

    // Copy functionality
    document.getElementById('copy-btn').addEventListener('click', function() {
        navigator.clipboard.writeText(confessionText.textContent);
        this.textContent = 'Copied!';
        setTimeout(() => this.textContent = 'Copy Text', 2000);
    });

    // Regenerate functionality
    document.getElementById('regenerate-btn').addEventListener('click', function() {
        form.dispatchEvent(new Event('submit'));
    });
});
</script>
@endsection
