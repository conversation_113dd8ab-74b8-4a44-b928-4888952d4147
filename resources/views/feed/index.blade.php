@extends('layouts.app')

@section('content')
<div class="max-w-6xl mx-auto">
    <!-- Feed Header -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div class="mb-4 lg:mb-0">
                <h1 class="text-3xl font-bold text-gray-900">Confession Feed</h1>
                <p class="text-gray-600">Discover the latest confessions from our community</p>
            </div>
            
            <!-- Quick Actions -->
            <div class="flex space-x-3">
                <a href="{{ route('confessions.ai-generator') }}" 
                   class="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-2 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all">
                    🤖 AI Generator
                </a>
                <a href="{{ route('confessions.create') }}" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    ✍️ Write Confession
                </a>
            </div>
        </div>
    </div>

    <!-- Filter Tabs -->
    <div class="bg-white rounded-lg shadow-sm mb-6">
        <div class="border-b border-gray-200">
            <nav class="flex space-x-8 px-6" aria-label="Tabs">
                @php
                    $filters = [
                        'trending' => ['name' => 'Trending', 'icon' => '🔥'],
                        'recent' => ['name' => 'Recent', 'icon' => '⏰'],
                        'popular' => ['name' => 'Popular', 'icon' => '❤️'],
                        'wildest' => ['name' => 'Wildest', 'icon' => '🌶️'],
                        'ai' => ['name' => 'AI Generated', 'icon' => '🤖'],
                        'user' => ['name' => 'User Created', 'icon' => '👤']
                    ];
                @endphp
                
                @foreach($filters as $key => $filterData)
                    <a href="{{ route('feed.index', ['filter' => $key, 'category' => request('category'), 'nsfw' => request('nsfw')]) }}" 
                       class="py-4 px-1 border-b-2 font-medium text-sm {{ $filter === $key ? 'border-purple-500 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                        {{ $filterData['icon'] }} {{ $filterData['name'] }}
                    </a>
                @endforeach
            </nav>
        </div>
        
        <!-- Category and NSFW Filters -->
        <div class="p-4 bg-gray-50">
            <div class="flex flex-wrap items-center gap-4">
                <!-- Category Filter -->
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">Category:</label>
                    <select id="category-filter" class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-purple-500 focus:border-purple-500">
                        <option value="">All Categories</option>
                        <option value="office" {{ $category === 'office' ? 'selected' : '' }}>💼 Office</option>
                        <option value="college" {{ $category === 'college' ? 'selected' : '' }}>🎓 College</option>
                        <option value="travel" {{ $category === 'travel' ? 'selected' : '' }}>✈️ Travel</option>
                        <option value="family" {{ $category === 'family' ? 'selected' : '' }}>👨‍👩‍👧‍👦 Family</option>
                        <option value="relationship" {{ $category === 'relationship' ? 'selected' : '' }}>💕 Relationship</option>
                        <option value="hookup" {{ $category === 'hookup' ? 'selected' : '' }}>🔥 Hookup</option>
                        <option value="fantasy" {{ $category === 'fantasy' ? 'selected' : '' }}>💭 Fantasy</option>
                        <option value="cheating" {{ $category === 'cheating' ? 'selected' : '' }}>💔 Cheating</option>
                        <option value="first-time" {{ $category === 'first-time' ? 'selected' : '' }}>🌟 First Time</option>
                        <option value="secret" {{ $category === 'secret' ? 'selected' : '' }}>🤫 Secret</option>
                    </select>
                </div>
                
                <!-- NSFW Toggle -->
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">NSFW:</label>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="nsfw-toggle" class="sr-only peer" {{ $nsfw ? 'checked' : '' }}>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                    </label>
                </div>
                
                <!-- Search -->
                <div class="flex-1 max-w-md">
                    <form action="{{ route('feed.search') }}" method="GET" class="flex">
                        <input type="text" name="q" placeholder="Search confessions..." 
                               value="{{ request('q') }}"
                               class="flex-1 border border-gray-300 rounded-l-md px-3 py-1 text-sm focus:ring-purple-500 focus:border-purple-500">
                        <button type="submit" class="bg-purple-600 text-white px-4 py-1 rounded-r-md hover:bg-purple-700 transition-colors">
                            🔍
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Confessions Grid -->
    <div id="confessions-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @foreach($confessions as $confession)
            @include('confessions.card', ['confession' => $confession])
        @endforeach
    </div>

    <!-- Load More Button -->
    @if($confessions->hasMorePages())
        <div class="text-center mt-8">
            <button id="load-more" data-page="{{ $confessions->currentPage() + 1 }}" 
                    class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
                Load More Confessions
            </button>
        </div>
    @endif

    <!-- Empty State -->
    @if($confessions->isEmpty())
        <div class="text-center py-12">
            <div class="text-6xl mb-4">😔</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">No confessions found</h3>
            <p class="text-gray-600 mb-6">Be the first to share a confession in this category!</p>
            <a href="{{ route('confessions.create') }}" 
               class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
                Write First Confession
            </a>
        </div>
    @endif
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const categoryFilter = document.getElementById('category-filter');
    const nsfwToggle = document.getElementById('nsfw-toggle');
    const loadMoreBtn = document.getElementById('load-more');
    const container = document.getElementById('confessions-container');
    
    // Filter change handlers
    function updateFilters() {
        const params = new URLSearchParams(window.location.search);
        params.set('category', categoryFilter.value);
        params.set('nsfw', nsfwToggle.checked ? '1' : '0');
        params.delete('page'); // Reset to first page
        window.location.search = params.toString();
    }
    
    categoryFilter.addEventListener('change', updateFilters);
    nsfwToggle.addEventListener('change', updateFilters);
    
    // Load more functionality
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', async function() {
            const page = this.dataset.page;
            const params = new URLSearchParams(window.location.search);
            params.set('page', page);
            
            try {
                this.disabled = true;
                this.textContent = 'Loading...';
                
                const response = await fetch(`{{ route('feed.index') }}?${params.toString()}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                const data = await response.json();
                
                // Append new confessions
                data.confessions.forEach(confession => {
                    const confessionHtml = createConfessionCard(confession);
                    container.insertAdjacentHTML('beforeend', confessionHtml);
                });
                
                // Update load more button
                if (data.hasMore) {
                    this.dataset.page = data.nextPage;
                    this.disabled = false;
                    this.textContent = 'Load More Confessions';
                } else {
                    this.remove();
                }
            } catch (error) {
                console.error('Error loading more confessions:', error);
                this.disabled = false;
                this.textContent = 'Load More Confessions';
            }
        });
    }
    
    function createConfessionCard(confession) {
        // This would need to be implemented to match your confession card template
        // For now, return a basic structure
        return `
            <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center text-white font-semibold">
                        ${confession.is_anonymous ? 'A' : confession.user?.name?.charAt(0) || 'U'}
                    </div>
                    <div class="ml-3">
                        <p class="font-medium">${confession.is_anonymous ? 'Anonymous' : confession.user?.name || 'User'}</p>
                        <p class="text-sm text-gray-500">${new Date(confession.created_at).toLocaleDateString()}</p>
                    </div>
                </div>
                <p class="text-gray-800 mb-4">${confession.content.substring(0, 200)}${confession.content.length > 200 ? '...' : ''}</p>
                <div class="flex items-center justify-between text-sm text-gray-500">
                    <span>❤️ ${confession.likes_count}</span>
                    <span>👁️ ${confession.views}</span>
                    <span>🔖 ${confession.bookmark_count}</span>
                </div>
            </div>
        `;
    }
});
</script>
@endsection
