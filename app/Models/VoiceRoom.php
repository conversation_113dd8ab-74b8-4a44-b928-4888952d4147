<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VoiceRoom extends Model
{
    protected $fillable = [
        'name',
        'description',
        'host_id',
        'is_active',
        'is_private',
        'max_participants',
        'current_participants',
        'category'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_private' => 'boolean',
    ];

    public function host()
    {
        return $this->belongsTo(User::class, 'host_id');
    }
}
