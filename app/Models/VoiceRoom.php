<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VoiceRoom extends Model
{
    protected $fillable = [
        'name',
        'description',
        'host_id',
        'is_active',
        'is_private',
        'max_participants',
        'current_participants',
        'category'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_private' => 'boolean',
    ];

    public function host()
    {
        return $this->belongsTo(User::class, 'host_id');
    }

    public function participants()
    {
        return $this->hasMany(VoiceRoomParticipant::class);
    }

    public function activeParticipants()
    {
        return $this->hasMany(VoiceRoomParticipant::class)->active();
    }

    public function speakingParticipants()
    {
        return $this->hasMany(VoiceRoomParticipant::class)->where('is_speaking', true);
    }

    /**
     * Get all users in the room (through participants)
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'voice_room_participants')
                    ->withPivot(['joined_at', 'status', 'role', 'is_muted_by_host', 'is_self_muted', 'is_speaking'])
                    ->withTimestamps();
    }

    /**
     * Check if user is in the room
     */
    public function hasParticipant($user)
    {
        return $this->participants()->where('user_id', $user->id)->active()->exists();
    }

    /**
     * Add a participant to the room
     */
    public function addParticipant($user, $role = VoiceRoomParticipant::ROLE_PARTICIPANT)
    {
        if (!$this->hasParticipant($user)) {
            $this->participants()->create([
                'user_id' => $user->id,
                'role' => $role,
                'status' => VoiceRoomParticipant::STATUS_ACTIVE
            ]);

            $this->increment('current_participants');
        }
    }

    /**
     * Remove a participant from the room
     */
    public function removeParticipant($user)
    {
        $participant = $this->participants()->where('user_id', $user->id)->active()->first();

        if ($participant) {
            $participant->update([
                'left_at' => now(),
                'status' => VoiceRoomParticipant::STATUS_KICKED
            ]);

            $this->decrement('current_participants');
        }
    }

    /**
     * Check if room is full
     */
    public function isFull()
    {
        return $this->current_participants >= $this->max_participants;
    }

    /**
     * Get participant by user
     */
    public function getParticipant($user)
    {
        return $this->participants()->where('user_id', $user->id)->active()->first();
    }
}
