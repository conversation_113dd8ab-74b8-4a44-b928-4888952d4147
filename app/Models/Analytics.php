<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Analytics extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_type',
        'event_name',
        'user_id',
        'session_id',
        'ip_address',
        'user_agent',
        'url',
        'referrer',
        'metadata',
        'value',
    ];

    protected $casts = [
        'metadata' => 'array',
        'value' => 'decimal:2',
    ];

    // Event types
    const TYPE_PAGE_VIEW = 'page_view';
    const TYPE_USER_ACTION = 'user_action';
    const TYPE_ENGAGEMENT = 'engagement';
    const TYPE_CONVERSION = 'conversion';
    const TYPE_ERROR = 'error';

    // Event names
    const EVENT_LOGIN = 'login';
    const EVENT_REGISTER = 'register';
    const EVENT_CONFESSION_VIEW = 'confession_view';
    const EVENT_CONFESSION_CREATE = 'confession_create';
    const EVENT_CONFESSION_LIKE = 'confession_like';
    const EVENT_CONFESSION_SHARE = 'confession_share';
    const EVENT_CONFESSION_BOOKMARK = 'confession_bookmark';
    const EVENT_USER_FOLLOW = 'user_follow';
    const EVENT_MESSAGE_SEND = 'message_send';
    const EVENT_SEARCH = 'search';
    const EVENT_PREMIUM_UPGRADE = 'premium_upgrade';

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', Carbon::today());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('created_at', [
            Carbon::now()->startOfWeek(),
            Carbon::now()->endOfWeek()
        ]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', Carbon::now()->month)
                    ->whereYear('created_at', Carbon::now()->year);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('event_type', $type);
    }

    public function scopeByEvent($query, $event)
    {
        return $query->where('event_name', $event);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    // Static analytics methods
    public static function track($eventType, $eventName, $userId = null, $metadata = [], $value = null)
    {
        return static::create([
            'event_type' => $eventType,
            'event_name' => $eventName,
            'user_id' => $userId,
            'session_id' => session()->getId(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'referrer' => request()->header('referer'),
            'metadata' => $metadata,
            'value' => $value,
        ]);
    }

    public static function getPageViews($period = 'today')
    {
        $query = static::byType(static::TYPE_PAGE_VIEW);
        
        if ($period === 'today') {
            $query = $query->today();
        } elseif ($period === 'week') {
            $query = $query->thisWeek();
        } elseif ($period === 'month') {
            $query = $query->thisMonth();
        }

        return $query->count();
    }

    public static function getUniqueVisitors($period = 'today')
    {
        $query = static::byType(static::TYPE_PAGE_VIEW);
        
        if ($period === 'today') {
            $query = $query->today();
        } elseif ($period === 'week') {
            $query = $query->thisWeek();
        } elseif ($period === 'month') {
            $query = $query->thisMonth();
        }

        return $query->distinct('ip_address')->count('ip_address');
    }

    public static function getTopPages($period = 'today', $limit = 10)
    {
        $query = static::byType(static::TYPE_PAGE_VIEW);
        
        if ($period === 'today') {
            $query = $query->today();
        } elseif ($period === 'week') {
            $query = $query->thisWeek();
        } elseif ($period === 'month') {
            $query = $query->thisMonth();
        }

        return $query->selectRaw('url, COUNT(*) as views')
                    ->groupBy('url')
                    ->orderBy('views', 'desc')
                    ->limit($limit)
                    ->get();
    }

    public static function getUserEngagement($period = 'today')
    {
        $query = static::byType(static::TYPE_ENGAGEMENT);
        
        if ($period === 'today') {
            $query = $query->today();
        } elseif ($period === 'week') {
            $query = $query->thisWeek();
        } elseif ($period === 'month') {
            $query = $query->thisMonth();
        }

        return $query->selectRaw('event_name, COUNT(*) as count')
                    ->groupBy('event_name')
                    ->orderBy('count', 'desc')
                    ->get();
    }

    public static function getConversionRate($period = 'today')
    {
        $visitors = static::getUniqueVisitors($period);
        
        $query = static::byType(static::TYPE_CONVERSION);
        
        if ($period === 'today') {
            $query = $query->today();
        } elseif ($period === 'week') {
            $query = $query->thisWeek();
        } elseif ($period === 'month') {
            $query = $query->thisMonth();
        }

        $conversions = $query->distinct('user_id')->count('user_id');

        return $visitors > 0 ? ($conversions / $visitors) * 100 : 0;
    }

    public static function getHourlyData($date = null)
    {
        $date = $date ?? Carbon::today();

        return static::whereDate('created_at', $date)
                    ->selectRaw('strftime("%H", created_at) as hour, COUNT(*) as events')
                    ->groupBy('hour')
                    ->orderBy('hour')
                    ->get();
    }

    public static function getDailyData($days = 30)
    {
        return static::where('created_at', '>=', Carbon::now()->subDays($days))
                    ->selectRaw('DATE(created_at) as date, COUNT(*) as events')
                    ->groupBy('date')
                    ->orderBy('date')
                    ->get();
    }

    public static function getReferrerData($period = 'today')
    {
        $query = static::byType(static::TYPE_PAGE_VIEW)
                      ->whereNotNull('referrer');
        
        if ($period === 'today') {
            $query = $query->today();
        } elseif ($period === 'week') {
            $query = $query->thisWeek();
        } elseif ($period === 'month') {
            $query = $query->thisMonth();
        }

        return $query->selectRaw('referrer, COUNT(*) as visits')
                    ->groupBy('referrer')
                    ->orderBy('visits', 'desc')
                    ->limit(10)
                    ->get();
    }

    public static function getUserRetention($cohortDate)
    {
        $newUsers = User::whereDate('created_at', $cohortDate)->pluck('id');
        
        $retentionData = [];
        for ($day = 1; $day <= 30; $day++) {
            $targetDate = Carbon::parse($cohortDate)->addDays($day);
            $activeUsers = static::whereDate('created_at', $targetDate)
                                ->whereIn('user_id', $newUsers)
                                ->distinct('user_id')
                                ->count('user_id');
            
            $retentionData[$day] = $newUsers->count() > 0 ? ($activeUsers / $newUsers->count()) * 100 : 0;
        }

        return $retentionData;
    }

    public static function getDeviceData($period = 'today')
    {
        $query = static::byType(static::TYPE_PAGE_VIEW);
        
        if ($period === 'today') {
            $query = $query->today();
        } elseif ($period === 'week') {
            $query = $query->thisWeek();
        } elseif ($period === 'month') {
            $query = $query->thisMonth();
        }

        return $query->selectRaw('
                CASE 
                    WHEN user_agent LIKE "%Mobile%" THEN "Mobile"
                    WHEN user_agent LIKE "%Tablet%" THEN "Tablet"
                    ELSE "Desktop"
                END as device_type,
                COUNT(*) as visits
            ')
            ->groupBy('device_type')
            ->get();
    }
}
