<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class VoiceRoomMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'voice_room_id',
        'user_id',
        'message',
        'message_type',
        'is_anonymous',
        'metadata',
        'edited_at',
        'is_deleted'
    ];

    protected $casts = [
        'is_anonymous' => 'boolean',
        'is_deleted' => 'boolean',
        'metadata' => 'array',
        'edited_at' => 'datetime'
    ];

    // Message type constants
    const TYPE_TEXT = 'text';
    const TYPE_SYSTEM = 'system';
    const TYPE_EMOJI = 'emoji';
    const TYPE_ANNOUNCEMENT = 'announcement';

    public function voiceRoom()
    {
        return $this->belongsTo(VoiceRoom::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the display name for the message sender
     */
    public function getSenderName()
    {
        if ($this->message_type === self::TYPE_SYSTEM) {
            return 'System';
        }

        if ($this->is_anonymous) {
            return 'Anonymous';
        }

        return $this->user ? $this->user->name : 'Unknown User';
    }

    /**
     * Get the display avatar for the message sender
     */
    public function getSenderAvatar()
    {
        if ($this->message_type === self::TYPE_SYSTEM || $this->is_anonymous) {
            return null;
        }

        return $this->user ? $this->user->avatar : null;
    }

    /**
     * Check if message can be edited by user
     */
    public function canEdit($user)
    {
        // System messages cannot be edited
        if ($this->message_type === self::TYPE_SYSTEM) {
            return false;
        }

        return $this->user_id === $user->id &&
               $this->created_at->diffInMinutes(now()) <= 15 &&
               !$this->is_deleted;
    }

    /**
     * Check if message can be deleted by user
     */
    public function canDelete($user)
    {
        // System messages cannot be deleted
        if ($this->message_type === self::TYPE_SYSTEM) {
            return false;
        }

        return $this->user_id === $user->id ||
               $this->voiceRoom->host_id === $user->id;
    }

    /**
     * Scope for non-deleted messages
     */
    public function scopeActive($query)
    {
        return $query->where('is_deleted', false);
    }

    /**
     * Scope for recent messages
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Format message for display
     */
    public function getFormattedMessage()
    {
        if ($this->is_deleted) {
            return '<em>This message was deleted</em>';
        }

        // Basic HTML escaping
        $message = htmlspecialchars($this->message);

        // Convert URLs to links
        $message = preg_replace(
            '/(https?:\/\/[^\s]+)/',
            '<a href="$1" target="_blank" class="text-blue-500 hover:underline">$1</a>',
            $message
        );

        // Convert mentions (@username) to links
        $message = preg_replace(
            '/@([a-zA-Z0-9_]+)/',
            '<span class="text-purple-600 font-medium">@$1</span>',
            $message
        );

        return $message;
    }

    /**
     * Create a system message
     */
    public static function createSystemMessage($voiceRoomId, $message, $metadata = [])
    {
        return self::create([
            'voice_room_id' => $voiceRoomId,
            'user_id' => null,
            'message' => $message,
            'message_type' => self::TYPE_SYSTEM,
            'metadata' => $metadata
        ]);
    }
}
