<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AiSettings extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
        'category',
        'is_active',
        'updated_by',
    ];

    protected $casts = [
        'value' => 'array',
        'is_active' => 'boolean',
    ];

    // Setting types
    const TYPE_STRING = 'string';
    const TYPE_NUMBER = 'number';
    const TYPE_BOOLEAN = 'boolean';
    const TYPE_ARRAY = 'array';
    const TYPE_JSON = 'json';

    // Categories
    const CATEGORY_MODELS = 'models';
    const CATEGORY_LIMITS = 'limits';
    const CATEGORY_COSTS = 'costs';
    const CATEGORY_FEATURES = 'features';
    const CATEGORY_SAFETY = 'safety';

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    // Static methods
    public static function get($key, $default = null)
    {
        $setting = static::where('key', $key)->where('is_active', true)->first();
        return $setting ? $setting->getValue() : $default;
    }

    public static function set($key, $value, $type = self::TYPE_STRING, $description = null, $category = null, $updatedBy = null)
    {
        return static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
                'description' => $description,
                'category' => $category,
                'is_active' => true,
                'updated_by' => $updatedBy,
            ]
        );
    }

    public function getValue()
    {
        switch ($this->type) {
            case self::TYPE_BOOLEAN:
                return (bool) $this->value;
            case self::TYPE_NUMBER:
                return (float) $this->value;
            case self::TYPE_ARRAY:
            case self::TYPE_JSON:
                return is_array($this->value) ? $this->value : json_decode($this->value, true);
            default:
                return $this->value;
        }
    }

    // Default settings
    public static function createDefaultSettings()
    {
        $settings = [
            // Model settings
            [
                'key' => 'default_model',
                'value' => 'mixtral-8x7b-32768',
                'type' => self::TYPE_STRING,
                'description' => 'Default AI model for confessions',
                'category' => self::CATEGORY_MODELS,
            ],
            [
                'key' => 'available_models',
                'value' => [
                    'mixtral-8x7b-32768' => ['name' => 'Mixtral 8x7B', 'cost_per_token' => 0.0000005],
                    'llama-3.1-70b-versatile' => ['name' => 'Llama 3.1 70B', 'cost_per_token' => 0.0000008],
                    'gemma-7b-it' => ['name' => 'Gemma 7B', 'cost_per_token' => 0.0000003],
                ],
                'type' => self::TYPE_JSON,
                'description' => 'Available AI models and their costs',
                'category' => self::CATEGORY_MODELS,
            ],
            
            // Limit settings
            [
                'key' => 'daily_request_limit',
                'value' => 1000,
                'type' => self::TYPE_NUMBER,
                'description' => 'Maximum AI requests per day',
                'category' => self::CATEGORY_LIMITS,
            ],
            [
                'key' => 'daily_cost_limit',
                'value' => 50.00,
                'type' => self::TYPE_NUMBER,
                'description' => 'Maximum AI cost per day (USD)',
                'category' => self::CATEGORY_LIMITS,
            ],
            [
                'key' => 'monthly_cost_limit',
                'value' => 1000.00,
                'type' => self::TYPE_NUMBER,
                'description' => 'Maximum AI cost per month (USD)',
                'category' => self::CATEGORY_LIMITS,
            ],
            [
                'key' => 'user_daily_limit',
                'value' => 10,
                'type' => self::TYPE_NUMBER,
                'description' => 'Maximum AI requests per user per day',
                'category' => self::CATEGORY_LIMITS,
            ],
            
            // Feature settings
            [
                'key' => 'ai_confessions_enabled',
                'value' => true,
                'type' => self::TYPE_BOOLEAN,
                'description' => 'Enable AI-generated confessions',
                'category' => self::CATEGORY_FEATURES,
            ],
            [
                'key' => 'auto_moderation_enabled',
                'value' => true,
                'type' => self::TYPE_BOOLEAN,
                'description' => 'Enable automatic content moderation',
                'category' => self::CATEGORY_FEATURES,
            ],
            [
                'key' => 'ai_chat_enabled',
                'value' => false,
                'type' => self::TYPE_BOOLEAN,
                'description' => 'Enable AI chat responses',
                'category' => self::CATEGORY_FEATURES,
            ],
            
            // Safety settings
            [
                'key' => 'content_filter_level',
                'value' => 'moderate',
                'type' => self::TYPE_STRING,
                'description' => 'Content filtering level (strict, moderate, relaxed)',
                'category' => self::CATEGORY_SAFETY,
            ],
            [
                'key' => 'auto_flag_threshold',
                'value' => 0.8,
                'type' => self::TYPE_NUMBER,
                'description' => 'Auto-flag content above this moderation score',
                'category' => self::CATEGORY_SAFETY,
            ],
            [
                'key' => 'blocked_keywords',
                'value' => ['spam', 'scam', 'illegal'],
                'type' => self::TYPE_ARRAY,
                'description' => 'Keywords that trigger automatic blocking',
                'category' => self::CATEGORY_SAFETY,
            ],
        ];

        foreach ($settings as $setting) {
            static::updateOrCreate(
                ['key' => $setting['key']],
                array_merge($setting, ['is_active' => true])
            );
        }
    }

    // Helper methods
    public static function isFeatureEnabled($feature)
    {
        return static::get($feature . '_enabled', false);
    }

    public static function getDailyLimit($type = 'request')
    {
        return static::get('daily_' . $type . '_limit', 0);
    }

    public static function getAvailableModels()
    {
        return static::get('available_models', []);
    }

    public static function getDefaultModel()
    {
        return static::get('default_model', 'mixtral-8x7b-32768');
    }

    public static function getModelCost($model)
    {
        $models = static::getAvailableModels();
        return $models[$model]['cost_per_token'] ?? 0;
    }
}
