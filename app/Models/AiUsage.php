<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class AiUsage extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'model_name',
        'prompt_tokens',
        'completion_tokens',
        'total_tokens',
        'cost_usd',
        'request_type',
        'prompt_template_id',
        'response_time_ms',
        'success',
        'error_message',
        'metadata',
    ];

    protected $casts = [
        'success' => 'boolean',
        'cost_usd' => 'decimal:6',
        'metadata' => 'array',
        'created_at' => 'datetime',
    ];

    // Request types
    const TYPE_CONFESSION = 'confession';
    const TYPE_MODERATION = 'moderation';
    const TYPE_CHAT = 'chat';
    const TYPE_SUMMARY = 'summary';

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function promptTemplate()
    {
        return $this->belongsTo(PromptTemplate::class);
    }

    // Scopes
    public function scopeSuccessful($query)
    {
        return $query->where('success', true);
    }

    public function scopeFailed($query)
    {
        return $query->where('success', false);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('created_at', Carbon::today());
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', Carbon::now()->month)
                    ->whereYear('created_at', Carbon::now()->year);
    }

    public function scopeByModel($query, $model)
    {
        return $query->where('model_name', $model);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('request_type', $type);
    }

    // Static methods for analytics
    public static function getTotalCostToday()
    {
        return static::today()->successful()->sum('cost_usd');
    }

    public static function getTotalCostThisMonth()
    {
        return static::thisMonth()->successful()->sum('cost_usd');
    }

    public static function getUsageByModel($period = 'today')
    {
        $query = static::successful();
        
        if ($period === 'today') {
            $query = $query->today();
        } elseif ($period === 'month') {
            $query = $query->thisMonth();
        }

        return $query->selectRaw('model_name, COUNT(*) as requests, SUM(total_tokens) as tokens, SUM(cost_usd) as cost')
                    ->groupBy('model_name')
                    ->get();
    }

    public static function getUsageByType($period = 'today')
    {
        $query = static::successful();
        
        if ($period === 'today') {
            $query = $query->today();
        } elseif ($period === 'month') {
            $query = $query->thisMonth();
        }

        return $query->selectRaw('request_type, COUNT(*) as requests, SUM(total_tokens) as tokens, SUM(cost_usd) as cost')
                    ->groupBy('request_type')
                    ->get();
    }

    public static function getDailyUsage($days = 7)
    {
        return static::successful()
                    ->where('created_at', '>=', Carbon::now()->subDays($days))
                    ->selectRaw('DATE(created_at) as date, COUNT(*) as requests, SUM(total_tokens) as tokens, SUM(cost_usd) as cost')
                    ->groupBy('date')
                    ->orderBy('date')
                    ->get();
    }

    public static function getAverageResponseTime($period = 'today')
    {
        $query = static::successful();
        
        if ($period === 'today') {
            $query = $query->today();
        } elseif ($period === 'month') {
            $query = $query->thisMonth();
        }

        return $query->avg('response_time_ms');
    }

    public static function getErrorRate($period = 'today')
    {
        $query = static::query();
        
        if ($period === 'today') {
            $query = $query->today();
        } elseif ($period === 'month') {
            $query = $query->thisMonth();
        }

        $total = $query->count();
        $failed = $query->failed()->count();

        return $total > 0 ? ($failed / $total) * 100 : 0;
    }

    // Helper method to log AI usage
    public static function logUsage($data)
    {
        return static::create([
            'user_id' => $data['user_id'] ?? null,
            'model_name' => $data['model_name'],
            'prompt_tokens' => $data['prompt_tokens'] ?? 0,
            'completion_tokens' => $data['completion_tokens'] ?? 0,
            'total_tokens' => $data['total_tokens'] ?? 0,
            'cost_usd' => $data['cost_usd'] ?? 0,
            'request_type' => $data['request_type'] ?? self::TYPE_CONFESSION,
            'prompt_template_id' => $data['prompt_template_id'] ?? null,
            'response_time_ms' => $data['response_time_ms'] ?? null,
            'success' => $data['success'] ?? true,
            'error_message' => $data['error_message'] ?? null,
            'metadata' => $data['metadata'] ?? [],
        ]);
    }
}
