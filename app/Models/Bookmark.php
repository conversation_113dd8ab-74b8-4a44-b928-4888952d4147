<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Bookmark extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'confession_id',
        'collection_name'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function confession()
    {
        return $this->belongsTo(Confession::class);
    }

    public function scopeInCollection($query, $collection)
    {
        return $query->where('collection_name', $collection);
    }
}
