<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WebRTCSignal extends Model
{
    use HasFactory;

    protected $table = 'webrtc_signals';

    protected $fillable = [
        'voice_room_id',
        'from_user_id',
        'to_user_id',
        'signal_type',
        'signal_data',
        'processed'
    ];

    protected $casts = [
        'signal_data' => 'array',
        'processed' => 'boolean'
    ];

    public function voiceRoom()
    {
        return $this->belongsTo(VoiceRoom::class);
    }

    public function fromUser()
    {
        return $this->belongsTo(User::class, 'from_user_id');
    }

    public function toUser()
    {
        return $this->belongsTo(User::class, 'to_user_id');
    }

    /**
     * Get pending signals for a user in a voice room
     */
    public static function getPendingSignals($voiceRoomId, $userId)
    {
        return self::where('voice_room_id', $voiceRoomId)
            ->where('to_user_id', $userId)
            ->where('processed', false)
            ->orderBy('created_at', 'asc')
            ->get();
    }

    /**
     * Mark signal as processed
     */
    public function markAsProcessed()
    {
        $this->update(['processed' => true]);
    }

    /**
     * Send a WebRTC signal
     */
    public static function sendSignal($voiceRoomId, $fromUserId, $toUserId, $signalType, $signalData)
    {
        return self::create([
            'voice_room_id' => $voiceRoomId,
            'from_user_id' => $fromUserId,
            'to_user_id' => $toUserId,
            'signal_type' => $signalType,
            'signal_data' => $signalData,
            'processed' => false
        ]);
    }
}
