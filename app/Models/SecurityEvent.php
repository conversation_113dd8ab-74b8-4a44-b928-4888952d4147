<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class SecurityEvent extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'event_type',
        'severity',
        'ip_address',
        'user_agent',
        'description',
        'metadata',
        'status',
        'resolved_at',
        'resolved_by',
        'action_taken',
    ];

    protected $casts = [
        'metadata' => 'array',
        'resolved_at' => 'datetime',
    ];

    // Event types
    const TYPE_FAILED_LOGIN = 'failed_login';
    const TYPE_SUSPICIOUS_ACTIVITY = 'suspicious_activity';
    const TYPE_SPAM_DETECTED = 'spam_detected';
    const TYPE_MALICIOUS_CONTENT = 'malicious_content';
    const TYPE_RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded';
    const TYPE_UNAUTHORIZED_ACCESS = 'unauthorized_access';
    const TYPE_DATA_BREACH_ATTEMPT = 'data_breach_attempt';
    const TYPE_ACCOUNT_TAKEOVER = 'account_takeover';
    const TYPE_PHISHING_ATTEMPT = 'phishing_attempt';
    const TYPE_DDOS_ATTACK = 'ddos_attack';

    // Severity levels
    const SEVERITY_LOW = 'low';
    const SEVERITY_MEDIUM = 'medium';
    const SEVERITY_HIGH = 'high';
    const SEVERITY_CRITICAL = 'critical';

    // Status
    const STATUS_OPEN = 'open';
    const STATUS_INVESTIGATING = 'investigating';
    const STATUS_RESOLVED = 'resolved';
    const STATUS_FALSE_POSITIVE = 'false_positive';

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function resolvedBy()
    {
        return $this->belongsTo(User::class, 'resolved_by');
    }

    // Scopes
    public function scopeOpen($query)
    {
        return $query->where('status', self::STATUS_OPEN);
    }

    public function scopeResolved($query)
    {
        return $query->where('status', self::STATUS_RESOLVED);
    }

    public function scopeBySeverity($query, $severity)
    {
        return $query->where('severity', $severity);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('event_type', $type);
    }

    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', Carbon::now()->subHours($hours));
    }

    public function scopeCritical($query)
    {
        return $query->where('severity', self::SEVERITY_CRITICAL);
    }

    // Static methods
    public static function log($eventType, $severity, $description, $userId = null, $metadata = [])
    {
        return static::create([
            'user_id' => $userId,
            'event_type' => $eventType,
            'severity' => $severity,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'description' => $description,
            'metadata' => $metadata,
            'status' => self::STATUS_OPEN,
        ]);
    }

    public static function getFailedLoginAttempts($ipAddress, $hours = 1)
    {
        return static::where('event_type', self::TYPE_FAILED_LOGIN)
                    ->where('ip_address', $ipAddress)
                    ->where('created_at', '>=', Carbon::now()->subHours($hours))
                    ->count();
    }

    public static function getSuspiciousIPs($hours = 24)
    {
        return static::where('created_at', '>=', Carbon::now()->subHours($hours))
                    ->selectRaw('ip_address, COUNT(*) as event_count')
                    ->groupBy('ip_address')
                    ->having('event_count', '>', 10)
                    ->orderBy('event_count', 'desc')
                    ->get();
    }

    public static function getSecurityMetrics($period = 'today')
    {
        $query = static::query();
        
        if ($period === 'today') {
            $query = $query->whereDate('created_at', Carbon::today());
        } elseif ($period === 'week') {
            $query = $query->where('created_at', '>=', Carbon::now()->subWeek());
        } elseif ($period === 'month') {
            $query = $query->where('created_at', '>=', Carbon::now()->subMonth());
        }

        return [
            'total_events' => $query->count(),
            'critical_events' => $query->where('severity', self::SEVERITY_CRITICAL)->count(),
            'open_events' => $query->where('status', self::STATUS_OPEN)->count(),
            'resolved_events' => $query->where('status', self::STATUS_RESOLVED)->count(),
            'failed_logins' => $query->where('event_type', self::TYPE_FAILED_LOGIN)->count(),
            'spam_detected' => $query->where('event_type', self::TYPE_SPAM_DETECTED)->count(),
        ];
    }

    public static function getThreatTrends($days = 30)
    {
        return static::where('created_at', '>=', Carbon::now()->subDays($days))
                    ->selectRaw('DATE(created_at) as date, event_type, COUNT(*) as count')
                    ->groupBy('date', 'event_type')
                    ->orderBy('date')
                    ->get();
    }

    public static function getTopThreats($period = 'week', $limit = 10)
    {
        $query = static::query();
        
        if ($period === 'today') {
            $query = $query->whereDate('created_at', Carbon::today());
        } elseif ($period === 'week') {
            $query = $query->where('created_at', '>=', Carbon::now()->subWeek());
        } elseif ($period === 'month') {
            $query = $query->where('created_at', '>=', Carbon::now()->subMonth());
        }

        return $query->selectRaw('event_type, COUNT(*) as count')
                    ->groupBy('event_type')
                    ->orderBy('count', 'desc')
                    ->limit($limit)
                    ->get();
    }

    public static function isIPBlocked($ipAddress)
    {
        $recentEvents = static::where('ip_address', $ipAddress)
                             ->where('created_at', '>=', Carbon::now()->subHour())
                             ->count();

        return $recentEvents >= 20; // Block if more than 20 events in last hour
    }

    public static function shouldBlockUser($userId)
    {
        $recentEvents = static::where('user_id', $userId)
                             ->where('severity', self::SEVERITY_HIGH)
                             ->where('created_at', '>=', Carbon::now()->subHours(24))
                             ->count();

        return $recentEvents >= 5; // Block if 5+ high severity events in 24 hours
    }

    public function resolve($actionTaken = null, $resolvedBy = null)
    {
        $this->update([
            'status' => self::STATUS_RESOLVED,
            'resolved_at' => now(),
            'resolved_by' => $resolvedBy ?? auth()->id(),
            'action_taken' => $actionTaken,
        ]);
    }

    public function markFalsePositive($resolvedBy = null)
    {
        $this->update([
            'status' => self::STATUS_FALSE_POSITIVE,
            'resolved_at' => now(),
            'resolved_by' => $resolvedBy ?? auth()->id(),
        ]);
    }

    // Helper methods
    public function getSeverityColor()
    {
        return match($this->severity) {
            self::SEVERITY_LOW => 'green',
            self::SEVERITY_MEDIUM => 'yellow',
            self::SEVERITY_HIGH => 'orange',
            self::SEVERITY_CRITICAL => 'red',
            default => 'gray'
        };
    }

    public function getSeverityBadge()
    {
        $color = $this->getSeverityColor();
        return "<span class='px-2 py-1 text-xs font-medium rounded-full bg-{$color}-100 text-{$color}-800'>" . 
               ucfirst($this->severity) . "</span>";
    }

    public function getStatusColor()
    {
        return match($this->status) {
            self::STATUS_OPEN => 'red',
            self::STATUS_INVESTIGATING => 'yellow',
            self::STATUS_RESOLVED => 'green',
            self::STATUS_FALSE_POSITIVE => 'gray',
            default => 'gray'
        };
    }

    public function getFormattedEventType()
    {
        return ucwords(str_replace('_', ' ', $this->event_type));
    }
}
