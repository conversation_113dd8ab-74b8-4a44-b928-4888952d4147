<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Notifications\DatabaseNotification;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'username',
        'email',
        'password',
        'google_id',
        'avatar',
        'theme',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function confessions()
    {
        return $this->hasMany(Confession::class);
    }

    public function likes()
    {
        return $this->hasMany(Like::class);
    }

    public function followers()
    {
        return $this->hasMany(Follow::class, 'following_id');
    }

    public function following()
    {
        return $this->hasMany(Follow::class, 'follower_id');
    }

    public function voiceRooms()
    {
        return $this->hasMany(VoiceRoom::class, 'host_id');
    }

    public function badges()
    {
        return $this->belongsToMany(Badge::class, 'user_badges');
    }

    public function hasBadge($badgeId)
    {
        return $this->badges()->where('badge_id', $badgeId)->exists();
    }

    public function comments()
    {
        return $this->hasMany(\App\Models\Comment::class);
    }

    // Messaging relationships
    public function sentMessages()
    {
        return $this->hasMany(Message::class, 'sender_id');
    }

    public function receivedMessages()
    {
        return $this->hasMany(Message::class, 'receiver_id');
    }

    // Friendship relationships
    public function friendships()
    {
        return $this->hasMany(Friendship::class);
    }

    public function friendRequests()
    {
        return $this->hasMany(Friendship::class, 'friend_id')->where('status', 'pending');
    }

    public function friends()
    {
        return $this->belongsToMany(User::class, 'friendships', 'user_id', 'friend_id')
                    ->wherePivot('status', 'accepted');
    }

    // Bookmark relationships
    public function bookmarks()
    {
        return $this->hasMany(Bookmark::class);
    }

    public function bookmarkedConfessions()
    {
        return $this->belongsToMany(Confession::class, 'bookmarks');
    }

    // Chat room relationships
    public function createdChatRooms()
    {
        return $this->hasMany(ChatRoom::class, 'creator_id');
    }

    public function chatRooms()
    {
        return $this->belongsToMany(ChatRoom::class, 'chat_room_members')
                    ->withPivot('joined_at', 'role')
                    ->withTimestamps();
    }

    public function chatMessages()
    {
        return $this->hasMany(ChatMessage::class);
    }

    // Helper methods
    public function isFriendsWith($user)
    {
        return $this->friends()->where('friend_id', $user->id)->exists();
    }

    public function hasFriendRequestFrom($user)
    {
        return $this->friendRequests()->where('user_id', $user->id)->exists();
    }

    public function hasSentFriendRequestTo($user)
    {
        return $this->friendships()->where('friend_id', $user->id)->where('status', 'pending')->exists();
    }

    public function isFollowing($user)
    {
        return $this->following()->where('following_id', $user->id)->exists();
    }

    public function hasBookmarked($confession)
    {
        return $this->bookmarks()->where('confession_id', $confession->id)->exists();
    }
}
