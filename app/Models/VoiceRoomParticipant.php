<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class VoiceRoomParticipant extends Model
{
    use HasFactory;

    protected $fillable = [
        'voice_room_id',
        'user_id',
        'joined_at',
        'left_at',
        'status',
        'is_muted_by_host',
        'is_self_muted',
        'is_speaking',
        'role',
        'permissions'
    ];

    protected $casts = [
        'joined_at' => 'datetime',
        'left_at' => 'datetime',
        'is_muted_by_host' => 'boolean',
        'is_self_muted' => 'boolean',
        'is_speaking' => 'boolean',
        'permissions' => 'array'
    ];

    // Status constants
    const STATUS_ACTIVE = 'active';
    const STATUS_MUTED = 'muted';
    const STATUS_SPEAKING = 'speaking';
    const STATUS_AWAY = 'away';
    const STATUS_KICKED = 'kicked';

    // Role constants
    const ROLE_PARTICIPANT = 'participant';
    const ROLE_MODERATOR = 'moderator';
    const ROLE_HOST = 'host';

    public function voiceRoom()
    {
        return $this->belongsTo(VoiceRoom::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if participant is effectively muted (by host or self)
     */
    public function isMuted()
    {
        return $this->is_muted_by_host || $this->is_self_muted;
    }

    /**
     * Check if participant can speak
     */
    public function canSpeak()
    {
        return $this->status === self::STATUS_ACTIVE && !$this->isMuted();
    }

    /**
     * Check if participant has moderator privileges
     */
    public function isModerator()
    {
        return in_array($this->role, [self::ROLE_MODERATOR, self::ROLE_HOST]);
    }

    /**
     * Check if participant is the host
     */
    public function isHost()
    {
        return $this->role === self::ROLE_HOST;
    }

    /**
     * Get participant's display status
     */
    public function getDisplayStatus()
    {
        if ($this->is_speaking) {
            return 'Speaking';
        }

        if ($this->is_muted_by_host) {
            return 'Muted by Host';
        }

        if ($this->is_self_muted) {
            return 'Muted';
        }

        return ucfirst($this->status);
    }

    /**
     * Scope for active participants
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE)->whereNull('left_at');
    }

    /**
     * Scope for participants who can speak
     */
    public function scopeCanSpeak($query)
    {
        return $query->active()
                    ->where('is_muted_by_host', false)
                    ->where('is_self_muted', false);
    }
}
