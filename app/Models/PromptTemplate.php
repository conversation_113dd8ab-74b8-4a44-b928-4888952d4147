<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PromptTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'template',
        'category',
        'is_active',
        'is_default',
        'variables',
        'model_settings',
        'usage_count',
        'success_rate',
        'average_rating',
        'created_by',
        'tags',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'variables' => 'array',
        'model_settings' => 'array',
        'success_rate' => 'decimal:2',
        'average_rating' => 'decimal:2',
        'tags' => 'array',
    ];

    // Categories
    const CATEGORY_CONFESSION = 'confession';
    const CATEGORY_MODERATION = 'moderation';
    const CATEGORY_CHAT = 'chat';
    const CATEGORY_SUMMARY = 'summary';
    const CATEGORY_CREATIVE = 'creative';

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function aiUsages()
    {
        return $this->hasMany(AiUsage::class);
    }

    public function ratings()
    {
        return $this->hasMany(PromptRating::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    // Methods
    public function render($variables = [])
    {
        $template = $this->template;
        
        foreach ($variables as $key => $value) {
            $template = str_replace('{{' . $key . '}}', $value, $template);
        }
        
        return $template;
    }

    public function incrementUsage()
    {
        $this->increment('usage_count');
    }

    public function updateSuccessRate()
    {
        $total = $this->aiUsages()->count();
        $successful = $this->aiUsages()->successful()->count();
        
        $this->success_rate = $total > 0 ? ($successful / $total) * 100 : 0;
        $this->save();
    }

    public function updateAverageRating()
    {
        $this->average_rating = $this->ratings()->avg('rating') ?? 0;
        $this->save();
    }

    public function getVariablesList()
    {
        preg_match_all('/\{\{(\w+)\}\}/', $this->template, $matches);
        return array_unique($matches[1]);
    }

    public function isValidTemplate()
    {
        $requiredVars = $this->variables ?? [];
        $templateVars = $this->getVariablesList();
        
        return empty(array_diff($requiredVars, $templateVars));
    }

    // Static methods
    public static function getDefaultForCategory($category)
    {
        return static::active()
                    ->byCategory($category)
                    ->default()
                    ->first();
    }

    public static function getPopularTemplates($limit = 10)
    {
        return static::active()
                    ->orderBy('usage_count', 'desc')
                    ->limit($limit)
                    ->get();
    }

    public static function getTopRatedTemplates($limit = 10)
    {
        return static::active()
                    ->orderBy('average_rating', 'desc')
                    ->limit($limit)
                    ->get();
    }

    // Default templates
    public static function createDefaultTemplates()
    {
        $templates = [
            [
                'name' => 'NSFW Confession Generator',
                'description' => 'Generates adult-themed confessions',
                'template' => 'Create a confession that is {{tone}} and {{content_type}}. The confession should be about {{topic}} and include {{mood}} elements. Make it {{length}} and ensure it feels authentic and relatable. The confession should be written in first person and be {{intensity}} in nature.',
                'category' => self::CATEGORY_CONFESSION,
                'is_active' => true,
                'is_default' => true,
                'variables' => ['tone', 'content_type', 'topic', 'mood', 'length', 'intensity'],
                'model_settings' => [
                    'temperature' => 0.8,
                    'max_tokens' => 500,
                    'top_p' => 0.9,
                ],
                'tags' => ['nsfw', 'adult', 'confession'],
            ],
            [
                'name' => 'Content Moderation',
                'description' => 'Analyzes content for moderation',
                'template' => 'Analyze this content for moderation: "{{content}}". Check for: inappropriate language, spam, harassment, explicit content, hate speech. Provide a moderation score (0-100) and explanation.',
                'category' => self::CATEGORY_MODERATION,
                'is_active' => true,
                'is_default' => true,
                'variables' => ['content'],
                'model_settings' => [
                    'temperature' => 0.1,
                    'max_tokens' => 200,
                ],
                'tags' => ['moderation', 'safety'],
            ],
            [
                'name' => 'Casual Chat Response',
                'description' => 'Generates casual chat responses',
                'template' => 'Respond to this message in a {{tone}} way: "{{message}}". Keep it {{length}} and {{style}}.',
                'category' => self::CATEGORY_CHAT,
                'is_active' => true,
                'is_default' => false,
                'variables' => ['tone', 'message', 'length', 'style'],
                'model_settings' => [
                    'temperature' => 0.7,
                    'max_tokens' => 150,
                ],
                'tags' => ['chat', 'conversation'],
            ],
            [
                'name' => 'Content Summary',
                'description' => 'Summarizes long content',
                'template' => 'Summarize this content in {{length}}: "{{content}}". Focus on {{focus_areas}}.',
                'category' => self::CATEGORY_SUMMARY,
                'is_active' => true,
                'is_default' => true,
                'variables' => ['length', 'content', 'focus_areas'],
                'model_settings' => [
                    'temperature' => 0.3,
                    'max_tokens' => 300,
                ],
                'tags' => ['summary', 'analysis'],
            ],
        ];

        foreach ($templates as $template) {
            static::updateOrCreate(
                ['name' => $template['name']],
                $template
            );
        }
    }
}
