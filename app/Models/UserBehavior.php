<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class UserBehavior extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'session_id',
        'action_type',
        'target_type',
        'target_id',
        'duration_seconds',
        'scroll_depth',
        'click_count',
        'metadata',
        'page_url',
        'timestamp',
    ];

    protected $casts = [
        'metadata' => 'array',
        'timestamp' => 'datetime',
        'duration_seconds' => 'integer',
        'scroll_depth' => 'integer',
        'click_count' => 'integer',
    ];

    // Action types
    const ACTION_PAGE_VIEW = 'page_view';
    const ACTION_SCROLL = 'scroll';
    const ACTION_CLICK = 'click';
    const ACTION_HOVER = 'hover';
    const ACTION_FORM_SUBMIT = 'form_submit';
    const ACTION_SEARCH = 'search';
    const ACTION_DOWNLOAD = 'download';
    const ACTION_SHARE = 'share';
    const ACTION_LIKE = 'like';
    const ACTION_COMMENT = 'comment';
    const ACTION_FOLLOW = 'follow';
    const ACTION_BOOKMARK = 'bookmark';

    // Target types
    const TARGET_CONFESSION = 'confession';
    const TARGET_USER = 'user';
    const TARGET_COMMENT = 'comment';
    const TARGET_PAGE = 'page';
    const TARGET_BUTTON = 'button';
    const TARGET_LINK = 'link';
    const TARGET_FORM = 'form';

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function target()
    {
        return $this->morphTo();
    }

    // Scopes
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', Carbon::today());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('created_at', [
            Carbon::now()->startOfWeek(),
            Carbon::now()->endOfWeek()
        ]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', Carbon::now()->month)
                    ->whereYear('created_at', Carbon::now()->year);
    }

    public function scopeByAction($query, $action)
    {
        return $query->where('action_type', $action);
    }

    public function scopeByTarget($query, $targetType)
    {
        return $query->where('target_type', $targetType);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    // Static tracking methods
    public static function track($userId, $actionType, $targetType = null, $targetId = null, $metadata = [])
    {
        return static::create([
            'user_id' => $userId,
            'session_id' => session()->getId(),
            'action_type' => $actionType,
            'target_type' => $targetType,
            'target_id' => $targetId,
            'metadata' => $metadata,
            'page_url' => request()->fullUrl(),
            'timestamp' => now(),
        ]);
    }

    // Analytics methods
    public static function getUserEngagementScore($userId, $period = 'week')
    {
        $query = static::byUser($userId);
        
        if ($period === 'today') {
            $query = $query->today();
        } elseif ($period === 'week') {
            $query = $query->thisWeek();
        } elseif ($period === 'month') {
            $query = $query->thisMonth();
        }

        $actions = $query->get();
        
        $score = 0;
        foreach ($actions as $action) {
            switch ($action->action_type) {
                case self::ACTION_PAGE_VIEW:
                    $score += 1;
                    break;
                case self::ACTION_LIKE:
                    $score += 3;
                    break;
                case self::ACTION_COMMENT:
                    $score += 5;
                    break;
                case self::ACTION_SHARE:
                    $score += 4;
                    break;
                case self::ACTION_FOLLOW:
                    $score += 6;
                    break;
                case self::ACTION_BOOKMARK:
                    $score += 2;
                    break;
                default:
                    $score += 1;
            }
        }

        return $score;
    }

    public static function getMostEngagedUsers($period = 'week', $limit = 10)
    {
        $query = static::with('user');
        
        if ($period === 'today') {
            $query = $query->today();
        } elseif ($period === 'week') {
            $query = $query->thisWeek();
        } elseif ($period === 'month') {
            $query = $query->thisMonth();
        }

        return $query->selectRaw('user_id, COUNT(*) as action_count')
                    ->groupBy('user_id')
                    ->orderBy('action_count', 'desc')
                    ->limit($limit)
                    ->get();
    }

    public static function getPopularContent($targetType, $period = 'week', $limit = 10)
    {
        $query = static::byTarget($targetType);
        
        if ($period === 'today') {
            $query = $query->today();
        } elseif ($period === 'week') {
            $query = $query->thisWeek();
        } elseif ($period === 'month') {
            $query = $query->thisMonth();
        }

        return $query->selectRaw('target_id, COUNT(*) as interaction_count')
                    ->groupBy('target_id')
                    ->orderBy('interaction_count', 'desc')
                    ->limit($limit)
                    ->get();
    }

    public static function getUserJourney($userId, $sessionId = null)
    {
        $query = static::byUser($userId)
                      ->orderBy('created_at');

        if ($sessionId) {
            $query = $query->where('session_id', $sessionId);
        }

        return $query->get();
    }

    public static function getAverageSessionDuration($period = 'today')
    {
        $query = static::query();
        
        if ($period === 'today') {
            $query = $query->today();
        } elseif ($period === 'week') {
            $query = $query->thisWeek();
        } elseif ($period === 'month') {
            $query = $query->thisMonth();
        }

        $sessions = $query->selectRaw('session_id, MIN(created_at) as start_time, MAX(created_at) as end_time')
                         ->groupBy('session_id')
                         ->get();

        $totalDuration = 0;
        $sessionCount = 0;

        foreach ($sessions as $session) {
            $duration = Carbon::parse($session->end_time)->diffInMinutes(Carbon::parse($session->start_time));
            if ($duration > 0 && $duration < 480) { // Exclude sessions longer than 8 hours (likely inactive)
                $totalDuration += $duration;
                $sessionCount++;
            }
        }

        return $sessionCount > 0 ? $totalDuration / $sessionCount : 0;
    }

    public static function getBounceRate($period = 'today')
    {
        $query = static::byAction(self::ACTION_PAGE_VIEW);
        
        if ($period === 'today') {
            $query = $query->today();
        } elseif ($period === 'week') {
            $query = $query->thisWeek();
        } elseif ($period === 'month') {
            $query = $query->thisMonth();
        }

        $sessions = $query->selectRaw('session_id, COUNT(*) as page_views')
                         ->groupBy('session_id')
                         ->get();

        $totalSessions = $sessions->count();
        $bouncedSessions = $sessions->where('page_views', 1)->count();

        return $totalSessions > 0 ? ($bouncedSessions / $totalSessions) * 100 : 0;
    }

    public static function getContentPerformance($targetType, $targetId, $period = 'week')
    {
        $query = static::byTarget($targetType)
                      ->where('target_id', $targetId);
        
        if ($period === 'today') {
            $query = $query->today();
        } elseif ($period === 'week') {
            $query = $query->thisWeek();
        } elseif ($period === 'month') {
            $query = $query->thisMonth();
        }

        return [
            'total_interactions' => $query->count(),
            'unique_users' => $query->distinct('user_id')->count('user_id'),
            'likes' => $query->byAction(self::ACTION_LIKE)->count(),
            'shares' => $query->byAction(self::ACTION_SHARE)->count(),
            'comments' => $query->byAction(self::ACTION_COMMENT)->count(),
            'bookmarks' => $query->byAction(self::ACTION_BOOKMARK)->count(),
            'views' => $query->byAction(self::ACTION_PAGE_VIEW)->count(),
        ];
    }

    public static function getHeatmapData($pageUrl, $period = 'week')
    {
        $query = static::where('page_url', $pageUrl)
                      ->whereIn('action_type', [self::ACTION_CLICK, self::ACTION_HOVER]);
        
        if ($period === 'today') {
            $query = $query->today();
        } elseif ($period === 'week') {
            $query = $query->thisWeek();
        } elseif ($period === 'month') {
            $query = $query->thisMonth();
        }

        return $query->selectRaw('action_type, metadata, COUNT(*) as count')
                    ->groupBy('action_type', 'metadata')
                    ->get();
    }

    public static function getFunnelAnalysis($steps, $period = 'week')
    {
        $results = [];
        
        foreach ($steps as $index => $step) {
            $query = static::byAction($step['action'])
                          ->byTarget($step['target']);
            
            if ($period === 'today') {
                $query = $query->today();
            } elseif ($period === 'week') {
                $query = $query->thisWeek();
            } elseif ($period === 'month') {
                $query = $query->thisMonth();
            }

            $count = $query->distinct('user_id')->count('user_id');
            $results[$index] = [
                'step' => $step['name'],
                'users' => $count,
                'conversion_rate' => $index > 0 ? ($count / $results[0]['users']) * 100 : 100,
            ];
        }

        return $results;
    }
}
