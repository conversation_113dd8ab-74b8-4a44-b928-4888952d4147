<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Friendship extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'friend_id',
        'status',
        'requested_at',
        'accepted_at'
    ];

    protected $casts = [
        'requested_at' => 'datetime',
        'accepted_at' => 'datetime',
    ];

    const STATUS_PENDING = 'pending';
    const STATUS_ACCEPTED = 'accepted';
    const STATUS_BLOCKED = 'blocked';

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function friend()
    {
        return $this->belongsTo(User::class, 'friend_id');
    }

    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    public function scopeAccepted($query)
    {
        return $query->where('status', self::STATUS_ACCEPTED);
    }

    public function accept()
    {
        $this->update([
            'status' => self::STATUS_ACCEPTED,
            'accepted_at' => now()
        ]);

        // Create reciprocal friendship
        self::firstOrCreate([
            'user_id' => $this->friend_id,
            'friend_id' => $this->user_id,
        ], [
            'status' => self::STATUS_ACCEPTED,
            'requested_at' => $this->requested_at,
            'accepted_at' => now()
        ]);
    }

    public function reject()
    {
        $this->delete();
    }
}
