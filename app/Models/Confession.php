<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Confession extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'content',
        'is_anonymous',
        'category',
        'views',
        'likes_count',
        'image',
        'is_nsfw',
        'content_type',
        'bookmark_count',
        'share_count',
        'tags',
        'is_ai_generated',
        'friends_only',
        'ai_prompt',
        'tone'
    ];

    protected $casts = [
        'is_anonymous' => 'boolean',
        'is_nsfw' => 'boolean',
        'is_ai_generated' => 'boolean',
        'friends_only' => 'boolean',
        'tags' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function likes()
    {
        return $this->hasMany(Like::class);
    }

    public function isLikedBy($user)
    {
        return $this->likes()->where('user_id', $user->id)->exists();
    }

    public function reactions()
    {
        return $this->hasMany(Reaction::class);
    }

    public function getUserReaction($user)
    {
        if (!$user) return null;
        return $this->reactions()->where('user_id', $user->id)->first();
    }

    public function getReactionCounts()
    {
        return $this->reactions()
            ->selectRaw('type, count(*) as count')
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();
    }

    public function comments()
    {
        return $this->hasMany(Comment::class);
    }

    public function bookmarks()
    {
        return $this->hasMany(Bookmark::class);
    }

    public function bookmarkedBy()
    {
        return $this->belongsToMany(User::class, 'bookmarks');
    }

    public function isBookmarkedBy($user)
    {
        return $this->bookmarks()->where('user_id', $user->id)->exists();
    }

    public function messages()
    {
        return $this->hasMany(Message::class);
    }

    public function chatMessages()
    {
        return $this->hasMany(ChatMessage::class);
    }

    // Scopes for filtering
    public function scopeNsfw($query)
    {
        return $query->where('is_nsfw', true);
    }

    public function scopeSfw($query)
    {
        return $query->where('is_nsfw', false);
    }

    public function scopeShort($query)
    {
        return $query->where('content_type', 'short');
    }

    public function scopeLong($query)
    {
        return $query->where('content_type', 'long');
    }

    public function scopeAiGenerated($query)
    {
        return $query->where('is_ai_generated', true);
    }

    public function scopeUserGenerated($query)
    {
        return $query->where('is_ai_generated', false);
    }

    public function scopeFriendsOnly($query)
    {
        return $query->where('friends_only', true);
    }

    public function scopePublic($query)
    {
        return $query->where('friends_only', false);
    }

    public function scopeTrending($query)
    {
        return $query->orderByRaw('(likes_count + bookmark_count + share_count) DESC')
                    ->where('created_at', '>=', now()->subDays(7));
    }

    public function scopeWildest($query)
    {
        return $query->where('is_nsfw', true)
                    ->orderByRaw('(likes_count + views) DESC');
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeWithTag($query, $tag)
    {
        return $query->whereJsonContains('tags', $tag);
    }

    // Helper methods
    public function incrementBookmarks()
    {
        $this->increment('bookmark_count');
    }

    public function decrementBookmarks()
    {
        $this->decrement('bookmark_count');
    }

    public function incrementShares()
    {
        $this->increment('share_count');
    }

    public function getEngagementScore()
    {
        return $this->likes_count + $this->bookmark_count + $this->share_count + ($this->views * 0.1);
    }
}
