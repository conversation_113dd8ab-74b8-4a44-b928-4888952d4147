<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class ContentModeration extends Model
{
    use HasFactory;

    protected $fillable = [
        'content_type',
        'content_id',
        'user_id',
        'moderation_type',
        'ai_score',
        'human_score',
        'status',
        'reason',
        'action_taken',
        'reviewed_by',
        'reviewed_at',
        'metadata',
        'auto_moderated',
    ];

    protected $casts = [
        'metadata' => 'array',
        'reviewed_at' => 'datetime',
        'auto_moderated' => 'boolean',
        'ai_score' => 'decimal:2',
        'human_score' => 'decimal:2',
    ];

    // Content types
    const CONTENT_CONFESSION = 'confession';
    const CONTENT_COMMENT = 'comment';
    const CONTENT_MESSAGE = 'message';
    const CONTENT_USER_PROFILE = 'user_profile';

    // Moderation types
    const TYPE_SPAM = 'spam';
    const TYPE_HATE_SPEECH = 'hate_speech';
    const TYPE_HARASSMENT = 'harassment';
    const TYPE_VIOLENCE = 'violence';
    const TYPE_ADULT_CONTENT = 'adult_content';
    const TYPE_MISINFORMATION = 'misinformation';
    const TYPE_COPYRIGHT = 'copyright';
    const TYPE_PERSONAL_INFO = 'personal_info';
    const TYPE_SELF_HARM = 'self_harm';
    const TYPE_ILLEGAL_CONTENT = 'illegal_content';

    // Status
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    const STATUS_FLAGGED = 'flagged';
    const STATUS_ESCALATED = 'escalated';

    // Actions
    const ACTION_NONE = 'none';
    const ACTION_WARNING = 'warning';
    const ACTION_CONTENT_REMOVED = 'content_removed';
    const ACTION_USER_SUSPENDED = 'user_suspended';
    const ACTION_USER_BANNED = 'user_banned';
    const ACTION_CONTENT_EDITED = 'content_edited';

    public function content()
    {
        return $this->morphTo();
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function reviewer()
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    public function scopeFlagged($query)
    {
        return $query->where('status', self::STATUS_FLAGGED);
    }

    public function scopeAutoModerated($query)
    {
        return $query->where('auto_moderated', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('moderation_type', $type);
    }

    public function scopeByContentType($query, $contentType)
    {
        return $query->where('content_type', $contentType);
    }

    public function scopeHighRisk($query)
    {
        return $query->where('ai_score', '>=', 0.8);
    }

    // Static methods
    public static function moderate($contentType, $contentId, $content, $userId = null)
    {
        $aiScore = static::getAIScore($content);
        $moderationType = static::detectModerationIssues($content);
        
        $moderation = static::create([
            'content_type' => $contentType,
            'content_id' => $contentId,
            'user_id' => $userId,
            'moderation_type' => $moderationType,
            'ai_score' => $aiScore,
            'status' => $aiScore >= 0.8 ? self::STATUS_FLAGGED : self::STATUS_APPROVED,
            'auto_moderated' => true,
            'metadata' => [
                'content_length' => strlen($content),
                'detected_issues' => static::getDetectedIssues($content),
                'confidence' => $aiScore,
            ],
        ]);

        // Auto-take action if score is very high
        if ($aiScore >= 0.9) {
            $moderation->takeAction(self::ACTION_CONTENT_REMOVED, 'Auto-removed due to high AI confidence');
        }

        return $moderation;
    }

    public static function getAIScore($content)
    {
        // Simplified AI scoring - in production, this would call actual AI service
        $score = 0;
        
        // Check for spam patterns
        if (static::containsSpamPatterns($content)) {
            $score += 0.3;
        }
        
        // Check for hate speech
        if (static::containsHateSpeech($content)) {
            $score += 0.4;
        }
        
        // Check for personal information
        if (static::containsPersonalInfo($content)) {
            $score += 0.2;
        }
        
        // Check for excessive profanity
        if (static::containsExcessiveProfanity($content)) {
            $score += 0.3;
        }

        return min($score, 1.0);
    }

    public static function detectModerationIssues($content)
    {
        if (static::containsSpamPatterns($content)) {
            return self::TYPE_SPAM;
        }
        
        if (static::containsHateSpeech($content)) {
            return self::TYPE_HATE_SPEECH;
        }
        
        if (static::containsPersonalInfo($content)) {
            return self::TYPE_PERSONAL_INFO;
        }
        
        if (static::containsAdultContent($content)) {
            return self::TYPE_ADULT_CONTENT;
        }

        return null;
    }

    public static function getDetectedIssues($content)
    {
        $issues = [];
        
        if (static::containsSpamPatterns($content)) {
            $issues[] = 'spam_patterns';
        }
        
        if (static::containsHateSpeech($content)) {
            $issues[] = 'hate_speech';
        }
        
        if (static::containsPersonalInfo($content)) {
            $issues[] = 'personal_info';
        }
        
        if (static::containsAdultContent($content)) {
            $issues[] = 'adult_content';
        }

        return $issues;
    }

    // Content analysis methods
    private static function containsSpamPatterns($content)
    {
        $spamPatterns = [
            '/\b(buy now|click here|limited time|act now)\b/i',
            '/\b(free money|make money|earn \$\d+)\b/i',
            '/\b(viagra|cialis|pharmacy)\b/i',
            '/\b(lottery|winner|congratulations)\b/i',
        ];

        foreach ($spamPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }

        return false;
    }

    private static function containsHateSpeech($content)
    {
        $hateSpeechWords = [
            'hate', 'kill', 'die', 'terrorist', 'nazi',
            // Add more hate speech patterns
        ];

        $content = strtolower($content);
        foreach ($hateSpeechWords as $word) {
            if (strpos($content, $word) !== false) {
                return true;
            }
        }

        return false;
    }

    private static function containsPersonalInfo($content)
    {
        // Check for email patterns
        if (preg_match('/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/', $content)) {
            return true;
        }
        
        // Check for phone patterns
        if (preg_match('/\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/', $content)) {
            return true;
        }
        
        // Check for SSN patterns
        if (preg_match('/\b\d{3}-\d{2}-\d{4}\b/', $content)) {
            return true;
        }

        return false;
    }

    private static function containsAdultContent($content)
    {
        $adultKeywords = [
            'sex', 'porn', 'nude', 'naked', 'xxx',
            // Add more adult content keywords
        ];

        $content = strtolower($content);
        foreach ($adultKeywords as $keyword) {
            if (strpos($content, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }

    private static function containsExcessiveProfanity($content)
    {
        $profanityWords = [
            'fuck', 'shit', 'damn', 'bitch', 'asshole',
            // Add more profanity words
        ];

        $content = strtolower($content);
        $profanityCount = 0;
        
        foreach ($profanityWords as $word) {
            $profanityCount += substr_count($content, $word);
        }

        return $profanityCount >= 3; // Flag if 3+ profanity words
    }

    // Instance methods
    public function takeAction($action, $reason = null)
    {
        $this->update([
            'action_taken' => $action,
            'reason' => $reason,
            'status' => self::STATUS_REJECTED,
            'reviewed_at' => now(),
            'reviewed_by' => auth()->id(),
        ]);

        // Execute the action
        switch ($action) {
            case self::ACTION_CONTENT_REMOVED:
                $this->removeContent();
                break;
            case self::ACTION_USER_SUSPENDED:
                $this->suspendUser();
                break;
            case self::ACTION_USER_BANNED:
                $this->banUser();
                break;
            case self::ACTION_WARNING:
                $this->sendWarning();
                break;
        }
    }

    public function approve($reviewedBy = null)
    {
        $this->update([
            'status' => self::STATUS_APPROVED,
            'reviewed_at' => now(),
            'reviewed_by' => $reviewedBy ?? auth()->id(),
        ]);
    }

    public function escalate($reason = null)
    {
        $this->update([
            'status' => self::STATUS_ESCALATED,
            'reason' => $reason,
        ]);
    }

    private function removeContent()
    {
        // Implementation depends on content type
        switch ($this->content_type) {
            case self::CONTENT_CONFESSION:
                Confession::find($this->content_id)?->update(['is_hidden' => true]);
                break;
            case self::CONTENT_COMMENT:
                Comment::find($this->content_id)?->delete();
                break;
        }
    }

    private function suspendUser()
    {
        if ($this->user) {
            $this->user->update([
                'is_suspended' => true,
                'suspended_until' => Carbon::now()->addDays(7),
            ]);
        }
    }

    private function banUser()
    {
        if ($this->user) {
            $this->user->update(['is_banned' => true]);
        }
    }

    private function sendWarning()
    {
        // Send warning notification to user
        // Implementation would depend on notification system
    }

    // Analytics methods
    public static function getModerationStats($period = 'today')
    {
        $query = static::query();
        
        if ($period === 'today') {
            $query = $query->whereDate('created_at', Carbon::today());
        } elseif ($period === 'week') {
            $query = $query->where('created_at', '>=', Carbon::now()->subWeek());
        } elseif ($period === 'month') {
            $query = $query->where('created_at', '>=', Carbon::now()->subMonth());
        }

        return [
            'total_moderated' => $query->count(),
            'auto_moderated' => $query->where('auto_moderated', true)->count(),
            'pending_review' => $query->where('status', self::STATUS_PENDING)->count(),
            'flagged_content' => $query->where('status', self::STATUS_FLAGGED)->count(),
            'approved_content' => $query->where('status', self::STATUS_APPROVED)->count(),
            'rejected_content' => $query->where('status', self::STATUS_REJECTED)->count(),
            'avg_ai_score' => $query->avg('ai_score'),
        ];
    }

    public function getStatusColor()
    {
        return match($this->status) {
            self::STATUS_PENDING => 'yellow',
            self::STATUS_APPROVED => 'green',
            self::STATUS_REJECTED => 'red',
            self::STATUS_FLAGGED => 'orange',
            self::STATUS_ESCALATED => 'purple',
            default => 'gray'
        };
    }
}
