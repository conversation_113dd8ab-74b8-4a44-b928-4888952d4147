<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ChatRoom extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'creator_id',
        'is_private',
        'max_members',
        'category',
        'is_nsfw',
        'room_code'
    ];

    protected $casts = [
        'is_private' => 'boolean',
        'is_nsfw' => 'boolean',
    ];

    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    public function members()
    {
        return $this->belongsToMany(User::class, 'chat_room_members')
                    ->withPivot('joined_at', 'role')
                    ->withTimestamps();
    }

    public function messages()
    {
        return $this->hasMany(ChatMessage::class);
    }

    public function isMember($user)
    {
        return $this->members()->where('user_id', $user->id)->exists();
    }

    public function addMember($user, $role = 'member')
    {
        if (!$this->isMember($user)) {
            $this->members()->attach($user->id, [
                'joined_at' => now(),
                'role' => $role
            ]);
        }
    }

    public function removeMember($user)
    {
        $this->members()->detach($user->id);
    }

    public function scopePublic($query)
    {
        return $query->where('is_private', false);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }
}
