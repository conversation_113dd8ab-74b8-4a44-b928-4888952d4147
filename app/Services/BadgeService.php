<?php

namespace App\Services;

use App\Models\Badge;
use App\Models\User;

class BadgeService
{
    public static function checkAndAwardBadges(User $user)
    {
        $badges = Badge::all();
        
        foreach ($badges as $badge) {
            if (!$user->hasBadge($badge->id)) {
                if (self::meetsCondition($user, $badge->condition)) {
                    $user->badges()->attach($badge->id);
                }
            }
        }
    }
    
    private static function meetsCondition(User $user, string $condition): bool
    {
        switch ($condition) {
            case 'first_confession':
                return $user->confessions()->count() >= 1;
                
            case '5_confessions':
                return $user->confessions()->count() >= 5;
                
            case '10_likes':
                return $user->confessions()->withCount('likes')->get()->sum('likes_count') >= 10;
                
            case '10_comments':
                return $user->comments()->count() >= 10;
                
            case 'first_voice_room':
                return $user->voiceRooms()->count() >= 1;
                
            case '50_reactions':
                $totalReactions = 0;
                foreach ($user->confessions as $confession) {
                    $totalReactions += $confession->reactions()->count();
                }
                return $totalReactions >= 50;
                
            default:
                return false;
        }
    }
}