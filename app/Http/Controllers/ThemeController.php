<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ThemeController extends Controller
{
    public function toggle()
    {
        $user = Auth::user();
        $newTheme = $user->theme === 'dark' ? 'light' : 'dark';
        
        $user->update(['theme' => $newTheme]);
        
        return response()->json(['theme' => $newTheme]);
    }
}