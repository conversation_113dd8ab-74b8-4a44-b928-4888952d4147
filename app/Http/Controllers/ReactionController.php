<?php

namespace App\Http\Controllers;

use App\Models\Confession;
use App\Models\Reaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReactionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function toggle(Confession $confession, Request $request)
    {
        $request->validate([
            'type' => 'required|in:heart,support,sad,angry,laugh'
        ]);

        $reaction = Reaction::where('user_id', Auth::id())
            ->where('confession_id', $confession->id)
            ->where('type', $request->type)
            ->first();

        if ($reaction) {
            $reaction->delete();
            $reacted = false;
        } else {
            // Remove any existing reaction from this user on this confession
            Reaction::where('user_id', Auth::id())
                ->where('confession_id', $confession->id)
                ->delete();
                
            Reaction::create([
                'user_id' => Auth::id(),
                'confession_id' => $confession->id,
                'type' => $request->type
            ]);
            $reacted = true;
        }

        $reactions = Reaction::where('confession_id', $confession->id)
            ->selectRaw('type, count(*) as count')
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();

        if (request()->ajax()) {
            return response()->json([
                'reacted' => $reacted,
                'reactions' => $reactions,
                'user_reaction' => $reacted ? $request->type : null
            ]);
        }

        return back();
    }
}
