<?php

namespace App\Http\Controllers;

use App\Models\Confession;
use App\Services\BadgeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ConfessionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth')->except(['index', 'show']);
    }

    public function index(Request $request)
    {
        $query = Confession::with('user');
        
        if ($request->has('category') && $request->category) {
            $query->where('category', $request->category);
        }
        
        if ($request->has('search') && $request->search) {
            $searchTerm = $request->search;
            $query->where(function($q) use ($searchTerm) {
                $q->where('content', 'LIKE', '%' . $searchTerm . '%')
                  ->orWhere('category', 'LIKE', '%' . $searchTerm . '%');
            });
        }
        
        $confessions = $query->with('comments')->orderBy('created_at', 'desc')->paginate(10);
        $selectedCategory = $request->category;
        $searchTerm = $request->search;
            
        return view('confessions.index', compact('confessions', 'selectedCategory', 'searchTerm'));
    }

    public function create()
    {
        return view('confessions.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'content' => 'required|string|max:1000',
            'category' => 'nullable|string|max:50',
            'is_anonymous' => 'boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $imagePath = null;
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('confessions', 'public');
        }
        
        $confession = Confession::create([
            'user_id' => $request->is_anonymous ? null : Auth::id(),
            'content' => $request->content,
            'category' => $request->category,
            'is_anonymous' => $request->boolean('is_anonymous'),
            'image' => $imagePath
        ]);

        // Check and award badges
        if (!$request->is_anonymous) {
            BadgeService::checkAndAwardBadges(Auth::user());
        }

        return redirect()->route('confessions.index')
            ->with('success', 'Confession posted successfully!');
    }

    public function show(Confession $confession)
    {
        $confession->increment('views');
        $confession->load('user', 'likes', 'comments.user');
        
        return view('confessions.show', compact('confession'));
    }

    public function edit(Confession $confession)
    {
        if ($confession->user_id !== Auth::id()) {
            abort(403);
        }
        
        return view('confessions.edit', compact('confession'));
    }

    public function update(Request $request, Confession $confession)
    {
        if ($confession->user_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'content' => 'required|string|max:1000',
            'category' => 'nullable|string|max:50'
        ]);

        $confession->update([
            'content' => $request->content,
            'category' => $request->category
        ]);

        return redirect()->route('confessions.show', $confession)
            ->with('success', 'Confession updated successfully!');
    }

    public function destroy(Confession $confession)
    {
        if ($confession->user_id !== Auth::id()) {
            abort(403);
        }

        $confession->delete();
        
        return redirect()->route('confessions.index')
            ->with('success', 'Confession deleted successfully!');
    }
    
    public function random()
    {
        $confession = Confession::inRandomOrder()->first();
        
        if (!$confession) {
            return redirect()->route('confessions.index')
                ->with('error', 'No confessions available.');
        }
        
        return redirect()->route('confessions.show', $confession);
    }
}
