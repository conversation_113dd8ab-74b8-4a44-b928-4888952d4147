<?php

namespace App\Http\Controllers;

use App\Models\Confession;
use App\Services\BadgeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;

class ConfessionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth')->except(['index', 'show']);
    }

    public function index(Request $request)
    {
        $query = Confession::with('user');
        
        if ($request->has('category') && $request->category) {
            $query->where('category', $request->category);
        }
        
        if ($request->has('search') && $request->search) {
            $searchTerm = $request->search;
            $query->where(function($q) use ($searchTerm) {
                $q->where('content', 'LIKE', '%' . $searchTerm . '%')
                  ->orWhere('category', 'LIKE', '%' . $searchTerm . '%');
            });
        }
        
        $confessions = $query->with('comments')->orderBy('created_at', 'desc')->paginate(10);
        $selectedCategory = $request->category;
        $searchTerm = $request->search;
            
        return view('confessions.index', compact('confessions', 'selectedCategory', 'searchTerm'));
    }

    public function create()
    {
        return view('confessions.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'content' => 'required|string|max:1000',
            'category' => 'nullable|string|max:50',
            'is_anonymous' => 'boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $imagePath = null;
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('confessions', 'public');
        }
        
        $confession = Confession::create([
            'user_id' => $request->is_anonymous ? null : Auth::id(),
            'content' => $request->content,
            'category' => $request->category,
            'is_anonymous' => $request->boolean('is_anonymous'),
            'image' => $imagePath
        ]);

        // Check and award badges
        if (!$request->is_anonymous) {
            BadgeService::checkAndAwardBadges(Auth::user());
        }

        return redirect()->route('confessions.index')
            ->with('success', 'Confession posted successfully!');
    }

    public function show(Confession $confession)
    {
        $confession->increment('views');
        $confession->load('user', 'likes', 'comments.user');
        
        return view('confessions.show', compact('confession'));
    }

    public function edit(Confession $confession)
    {
        if ($confession->user_id !== Auth::id()) {
            abort(403);
        }
        
        return view('confessions.edit', compact('confession'));
    }

    public function update(Request $request, Confession $confession)
    {
        if ($confession->user_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'content' => 'required|string|max:1000',
            'category' => 'nullable|string|max:50'
        ]);

        $confession->update([
            'content' => $request->content,
            'category' => $request->category
        ]);

        return redirect()->route('confessions.show', $confession)
            ->with('success', 'Confession updated successfully!');
    }

    public function destroy(Confession $confession)
    {
        if ($confession->user_id !== Auth::id()) {
            abort(403);
        }

        $confession->delete();
        
        return redirect()->route('confessions.index')
            ->with('success', 'Confession deleted successfully!');
    }
    
    public function random()
    {
        $confession = Confession::inRandomOrder()->first();

        if (!$confession) {
            return redirect()->route('confessions.index')
                ->with('error', 'No confessions available.');
        }

        return redirect()->route('confessions.show', $confession);
    }

    public function showAIGenerator()
    {
        return view('confessions.ai-generator');
    }

    public function generateConfession(Request $request)
    {
        $request->validate([
            'tone' => 'nullable|string|in:wild,romantic,dramatic,funny,dark,mysterious',
            'category' => 'nullable|string|max:50',
            'content_type' => 'nullable|string|in:short,long',
            'is_nsfw' => 'boolean',
            'custom_prompt' => 'nullable|string|max:500',
            'sample_confessions' => 'nullable|array',
            'sample_confessions.*' => 'string|max:1000'
        ]);

        try {
            $tone = $request->tone ?: 'wild';
            $category = $request->category ?: 'general';
            $contentType = $request->content_type ?: 'long';
            $isNsfw = $request->boolean('is_nsfw', true);
            $customPrompt = $request->custom_prompt;
            $sampleConfessions = $request->sample_confessions ?: [];

            $contentPrompt = $this->buildAIPrompt($tone, $category, $contentType, $isNsfw, $customPrompt, $sampleConfessions);

            $response = Http::withToken(env('GROQ_API_KEY'))
                ->timeout(30)
                ->post('https://api.groq.com/openai/v1/chat/completions', [
                    'model' => 'mixtral-8x7b-32768',
                    'messages' => [
                        [
                            'role' => 'system',
                            'content' => $this->getSystemPrompt($tone, $isNsfw)
                        ],
                        [
                            'role' => 'user',
                            'content' => $contentPrompt
                        ]
                    ],
                    'temperature' => $this->getTemperature($tone),
                    'max_tokens' => $contentType === 'short' ? 300 : 700,
                ]);

            if ($response->failed()) {
                return response()->json([
                    'error' => 'Failed to generate confession. Please try again.'
                ], 500);
            }

            $confession = $response->json('choices.0.message.content');

            if (!$confession) {
                return response()->json([
                    'error' => 'No confession content received.'
                ], 500);
            }

            // Optionally save the AI-generated confession
            if ($request->boolean('save_confession')) {
                $savedConfession = Confession::create([
                    'user_id' => $request->boolean('is_anonymous') ? null : Auth::id(),
                    'content' => $confession,
                    'category' => $category,
                    'is_anonymous' => $request->boolean('is_anonymous', true),
                    'is_nsfw' => $isNsfw,
                    'content_type' => $contentType,
                    'is_ai_generated' => true,
                    'ai_prompt' => $customPrompt,
                    'tone' => $tone,
                    'tags' => $this->extractTags($category, $tone)
                ]);

                return response()->json([
                    'confession' => $confession,
                    'saved' => true,
                    'confession_id' => $savedConfession->id
                ]);
            }

            return response()->json(['confession' => $confession]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'An error occurred while generating the confession.'
            ], 500);
        }
    }

    private function buildAIPrompt($tone, $category, $contentType, $isNsfw, $customPrompt, $sampleConfessions)
    {
        $basePrompt = "Write a first-person confession";

        // Add sample confessions for style reference
        if (!empty($sampleConfessions)) {
            $basePrompt .= "\n\nHere are some sample confessions for style reference:\n";
            foreach ($sampleConfessions as $sample) {
                $basePrompt .= "- " . $sample . "\n";
            }
            $basePrompt .= "\nWrite a similar style confession but with completely different content.";
        }

        // Add tone specifications
        $toneInstructions = [
            'wild' => 'Make it wild, shocking, and bold. Use explicit language and vivid details.',
            'romantic' => 'Make it romantic and passionate. Focus on emotions and intimate moments.',
            'dramatic' => 'Make it dramatic and intense. Include conflict, tension, or life-changing moments.',
            'funny' => 'Make it humorous and entertaining. Include funny situations or embarrassing moments.',
            'dark' => 'Make it dark and mysterious. Include secrets, forbidden desires, or taboo subjects.',
            'mysterious' => 'Make it mysterious and intriguing. Leave some things unsaid and create suspense.'
        ];

        $basePrompt .= "\n\nTone: " . ($toneInstructions[$tone] ?? $toneInstructions['wild']);

        // Add category context
        if ($category && $category !== 'general') {
            $basePrompt .= "\nCategory: Focus on themes related to " . $category;
        }

        // Add NSFW specification
        if ($isNsfw) {
            $basePrompt .= "\nContent: This should be NSFW (Not Safe For Work) with adult themes, sexual content, or mature situations.";
        } else {
            $basePrompt .= "\nContent: Keep this SFW (Safe For Work) without explicit sexual content.";
        }

        // Add length specification
        $lengthSpec = $contentType === 'short'
            ? "Keep it concise, like a tweet or short post (100-200 words)."
            : "Make it detailed and immersive (300-500 words).";

        $basePrompt .= "\nLength: " . $lengthSpec;

        // Add custom prompt if provided
        if ($customPrompt) {
            $basePrompt .= "\n\nSpecial instructions: " . $customPrompt;
        }

        $basePrompt .= "\n\nUse casual, realistic language like Reddit or adult forums. Make it emotional and authentic.";

        return $basePrompt;
    }

    private function getSystemPrompt($tone, $isNsfw)
    {
        $baseSystem = "You are an expert confession writer who creates authentic, engaging first-person stories.";

        if ($isNsfw) {
            $baseSystem .= " You can write mature, adult content including sexual themes when appropriate.";
        } else {
            $baseSystem .= " Keep content appropriate and avoid explicit sexual material.";
        }

        $baseSystem .= " Write in a natural, conversational style that feels genuine and relatable.";

        return $baseSystem;
    }

    private function getTemperature($tone)
    {
        $temperatures = [
            'wild' => 0.9,
            'romantic' => 0.7,
            'dramatic' => 0.8,
            'funny' => 0.8,
            'dark' => 0.85,
            'mysterious' => 0.75
        ];

        return $temperatures[$tone] ?? 0.8;
    }

    private function extractTags($category, $tone)
    {
        $tags = [];

        if ($category && $category !== 'general') {
            $tags[] = $category;
        }

        $tags[] = $tone;

        return $tags;
    }
}
