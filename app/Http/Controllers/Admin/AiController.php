<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AiUsage;
use App\Models\PromptTemplate;
use App\Models\AiSettings;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AiController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    public function index()
    {
        // Get overview stats
        $stats = [
            'total_requests_today' => AiUsage::today()->count(),
            'total_cost_today' => AiUsage::getTotalCostToday(),
            'total_requests_month' => AiUsage::thisMonth()->count(),
            'total_cost_month' => AiUsage::getTotalCostThisMonth(),
            'success_rate_today' => 100 - AiUsage::getErrorRate('today'),
            'avg_response_time' => AiUsage::getAverageResponseTime('today'),
            'active_templates' => PromptTemplate::active()->count(),
            'daily_limit' => AiSettings::getDailyLimit('cost'),
        ];

        // Get usage by model
        $modelUsage = AiUsage::getUsageByModel('today');
        
        // Get usage by type
        $typeUsage = AiUsage::getUsageByType('today');
        
        // Get daily usage for chart
        $dailyUsage = AiUsage::getDailyUsage(7);
        
        // Get recent activity
        $recentActivity = AiUsage::with('user')
            ->latest()
            ->limit(10)
            ->get();

        return view('admin.ai.index', compact(
            'stats',
            'modelUsage',
            'typeUsage',
            'dailyUsage',
            'recentActivity'
        ));
    }

    public function usage()
    {
        $period = request('period', 'today');
        $model = request('model');
        $type = request('type');

        $query = AiUsage::with('user');

        // Apply filters
        if ($period === 'today') {
            $query->today();
        } elseif ($period === 'week') {
            $query->where('created_at', '>=', Carbon::now()->subWeek());
        } elseif ($period === 'month') {
            $query->thisMonth();
        }

        if ($model) {
            $query->byModel($model);
        }

        if ($type) {
            $query->byType($type);
        }

        $usages = $query->latest()->paginate(50);

        // Get filter options
        $models = AiUsage::distinct()->pluck('model_name');
        $types = AiUsage::distinct()->pluck('request_type');

        return view('admin.ai.usage', compact('usages', 'models', 'types'));
    }

    public function templates()
    {
        $templates = PromptTemplate::with('creator')
            ->when(request('category'), function ($query, $category) {
                return $query->byCategory($category);
            })
            ->when(request('search'), function ($query, $search) {
                return $query->where('name', 'like', "%{$search}%")
                           ->orWhere('description', 'like', "%{$search}%");
            })
            ->latest()
            ->paginate(20);

        $categories = [
            PromptTemplate::CATEGORY_CONFESSION => 'Confession',
            PromptTemplate::CATEGORY_MODERATION => 'Moderation',
            PromptTemplate::CATEGORY_CHAT => 'Chat',
            PromptTemplate::CATEGORY_SUMMARY => 'Summary',
            PromptTemplate::CATEGORY_CREATIVE => 'Creative',
        ];

        return view('admin.ai.templates', compact('templates', 'categories'));
    }

    public function createTemplate()
    {
        $categories = [
            PromptTemplate::CATEGORY_CONFESSION => 'Confession',
            PromptTemplate::CATEGORY_MODERATION => 'Moderation',
            PromptTemplate::CATEGORY_CHAT => 'Chat',
            PromptTemplate::CATEGORY_SUMMARY => 'Summary',
            PromptTemplate::CATEGORY_CREATIVE => 'Creative',
        ];

        return view('admin.ai.create-template', compact('categories'));
    }

    public function storeTemplate(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'template' => 'required|string',
            'category' => 'required|string',
            'variables' => 'nullable|array',
            'model_settings' => 'nullable|array',
            'tags' => 'nullable|array',
        ]);

        $template = PromptTemplate::create([
            'name' => $request->name,
            'description' => $request->description,
            'template' => $request->template,
            'category' => $request->category,
            'variables' => $request->variables ?? [],
            'model_settings' => $request->model_settings ?? [],
            'tags' => $request->tags ?? [],
            'created_by' => auth()->id(),
        ]);

        return redirect()->route('admin.ai.templates')
            ->with('success', 'Template created successfully!');
    }

    public function editTemplate(PromptTemplate $template)
    {
        $categories = [
            PromptTemplate::CATEGORY_CONFESSION => 'Confession',
            PromptTemplate::CATEGORY_MODERATION => 'Moderation',
            PromptTemplate::CATEGORY_CHAT => 'Chat',
            PromptTemplate::CATEGORY_SUMMARY => 'Summary',
            PromptTemplate::CATEGORY_CREATIVE => 'Creative',
        ];

        return view('admin.ai.edit-template', compact('template', 'categories'));
    }

    public function updateTemplate(Request $request, PromptTemplate $template)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'template' => 'required|string',
            'category' => 'required|string',
            'variables' => 'nullable|array',
            'model_settings' => 'nullable|array',
            'tags' => 'nullable|array',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
        ]);

        $template->update($request->all());

        return redirect()->route('admin.ai.templates')
            ->with('success', 'Template updated successfully!');
    }

    public function testTemplate(Request $request, PromptTemplate $template)
    {
        $request->validate([
            'variables' => 'nullable|array',
        ]);

        $variables = $request->variables ?? [];
        $renderedTemplate = $template->render($variables);

        return response()->json([
            'rendered_template' => $renderedTemplate,
            'variables_used' => $template->getVariablesList(),
        ]);
    }

    public function settings()
    {
        $settings = AiSettings::active()->get()->groupBy('category');

        return view('admin.ai.settings', compact('settings'));
    }

    public function updateSettings(Request $request)
    {
        foreach ($request->settings as $key => $value) {
            $setting = AiSettings::where('key', $key)->first();
            if ($setting) {
                $setting->update([
                    'value' => $value,
                    'updated_by' => auth()->id(),
                ]);
            }
        }

        return redirect()->route('admin.ai.settings')
            ->with('success', 'Settings updated successfully!');
    }

    public function costs()
    {
        $period = request('period', 'month');
        
        // Get cost breakdown
        $costData = [];
        if ($period === 'month') {
            $costData = AiUsage::successful()
                ->thisMonth()
                ->selectRaw('DATE(created_at) as date, SUM(cost_usd) as cost, COUNT(*) as requests')
                ->groupBy('date')
                ->orderBy('date')
                ->get();
        } elseif ($period === 'week') {
            $costData = AiUsage::successful()
                ->where('created_at', '>=', Carbon::now()->subWeek())
                ->selectRaw('DATE(created_at) as date, SUM(cost_usd) as cost, COUNT(*) as requests')
                ->groupBy('date')
                ->orderBy('date')
                ->get();
        }

        // Get cost by model
        $modelCosts = AiUsage::getUsageByModel($period === 'month' ? 'month' : 'today');

        // Get projections
        $dailyAvg = AiUsage::thisMonth()->avg('cost_usd') * AiUsage::thisMonth()->count();
        $monthlyProjection = $dailyAvg * 30;

        return view('admin.ai.costs', compact('costData', 'modelCosts', 'monthlyProjection'));
    }

    public function analytics()
    {
        // Performance metrics
        $metrics = [
            'avg_response_time_7d' => AiUsage::where('created_at', '>=', Carbon::now()->subDays(7))
                ->avg('response_time_ms'),
            'success_rate_7d' => 100 - AiUsage::getErrorRate('week'),
            'total_tokens_today' => AiUsage::today()->sum('total_tokens'),
            'peak_hour' => AiUsage::today()
                ->selectRaw('HOUR(created_at) as hour, COUNT(*) as requests')
                ->groupBy('hour')
                ->orderBy('requests', 'desc')
                ->first(),
        ];

        // Popular templates
        $popularTemplates = PromptTemplate::orderBy('usage_count', 'desc')
            ->limit(10)
            ->get();

        // Error analysis
        $errors = AiUsage::failed()
            ->where('created_at', '>=', Carbon::now()->subDays(7))
            ->selectRaw('error_message, COUNT(*) as count')
            ->groupBy('error_message')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();

        return view('admin.ai.analytics', compact('metrics', 'popularTemplates', 'errors'));
    }
}
