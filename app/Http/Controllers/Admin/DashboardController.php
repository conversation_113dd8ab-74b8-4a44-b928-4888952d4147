<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Confession;
use App\Models\Report;
use App\Models\Message;
use App\Models\ChatRoom;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        // Get key metrics
        $stats = $this->getOverviewStats();
        $recentActivity = $this->getRecentActivity();
        $chartData = $this->getChartData();
        $pendingReports = Report::pending()->with(['user', 'reportable'])->latest()->take(5)->get();
        $topUsers = $this->getTopUsers();
        
        return view('admin.dashboard', compact('stats', 'recentActivity', 'chartData', 'pendingReports', 'topUsers'));
    }

    private function getOverviewStats()
    {
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();
        $lastWeek = Carbon::now()->subWeek();
        $lastMonth = Carbon::now()->subMonth();

        return [
            'total_users' => User::count(),
            'new_users_today' => User::whereDate('created_at', $today)->count(),
            'new_users_yesterday' => User::whereDate('created_at', $yesterday)->count(),
            'active_users_week' => User::where('last_active_at', '>=', $lastWeek)->count(),
            
            'total_confessions' => Confession::count(),
            'confessions_today' => Confession::whereDate('created_at', $today)->count(),
            'confessions_yesterday' => Confession::whereDate('created_at', $yesterday)->count(),
            'ai_confessions' => Confession::where('is_ai_generated', true)->count(),
            'nsfw_confessions' => Confession::where('is_nsfw', true)->count(),
            
            'total_reports' => Report::count(),
            'pending_reports' => Report::pending()->count(),
            'reports_today' => Report::whereDate('created_at', $today)->count(),
            'high_priority_reports' => Report::highPriority()->pending()->count(),
            
            'total_messages' => Message::count(),
            'messages_today' => Message::whereDate('created_at', $today)->count(),
            'active_chat_rooms' => ChatRoom::whereHas('messages', function($q) use ($lastWeek) {
                $q->where('created_at', '>=', $lastWeek);
            })->count(),
            
            'banned_users' => User::where('is_banned', true)->count(),
            'verified_users' => User::where('is_verified', true)->count(),
        ];
    }

    private function getRecentActivity()
    {
        $activities = collect();

        // Recent user registrations
        $newUsers = User::latest()->take(5)->get()->map(function($user) {
            return [
                'type' => 'user_registered',
                'message' => "New user registered: {$user->name}",
                'time' => $user->created_at,
                'icon' => '👤',
                'color' => 'green'
            ];
        });

        // Recent confessions
        $newConfessions = Confession::with('user')->latest()->take(5)->get()->map(function($confession) {
            $type = $confession->is_ai_generated ? 'AI confession' : 'User confession';
            return [
                'type' => 'confession_created',
                'message' => "{$type} posted by " . ($confession->is_anonymous ? 'Anonymous' : $confession->user->name),
                'time' => $confession->created_at,
                'icon' => $confession->is_ai_generated ? '🤖' : '✍️',
                'color' => $confession->is_nsfw ? 'red' : 'blue'
            ];
        });

        // Recent reports
        $newReports = Report::with(['user', 'reportable'])->latest()->take(5)->get()->map(function($report) {
            return [
                'type' => 'report_created',
                'message' => "New report: {$report->reason} by {$report->user->name}",
                'time' => $report->created_at,
                'icon' => '🚨',
                'color' => 'red'
            ];
        });

        return $activities->merge($newUsers)->merge($newConfessions)->merge($newReports)
                          ->sortByDesc('time')->take(15)->values();
    }

    private function getChartData()
    {
        $days = collect(range(6, 0))->map(function($day) {
            return Carbon::now()->subDays($day);
        });

        $userRegistrations = $days->map(function($date) {
            return [
                'date' => $date->format('M j'),
                'count' => User::whereDate('created_at', $date)->count()
            ];
        });

        $confessionPosts = $days->map(function($date) {
            return [
                'date' => $date->format('M j'),
                'count' => Confession::whereDate('created_at', $date)->count()
            ];
        });

        $reportSubmissions = $days->map(function($date) {
            return [
                'date' => $date->format('M j'),
                'count' => Report::whereDate('created_at', $date)->count()
            ];
        });

        return [
            'user_registrations' => $userRegistrations,
            'confession_posts' => $confessionPosts,
            'report_submissions' => $reportSubmissions,
            'categories' => $this->getCategoryStats(),
            'engagement' => $this->getEngagementStats()
        ];
    }

    private function getCategoryStats()
    {
        return Confession::select('category', DB::raw('count(*) as count'))
                        ->whereNotNull('category')
                        ->groupBy('category')
                        ->orderByDesc('count')
                        ->take(10)
                        ->get();
    }

    private function getEngagementStats()
    {
        return [
            'total_likes' => DB::table('likes')->count(),
            'total_bookmarks' => DB::table('bookmarks')->count(),
            'total_shares' => Confession::sum('share_count'),
            'total_views' => Confession::sum('views'),
            'avg_engagement' => Confession::avg(DB::raw('likes_count + bookmark_count + share_count'))
        ];
    }

    private function getTopUsers()
    {
        return [
            'most_confessions' => User::withCount('confessions')
                                     ->orderByDesc('confessions_count')
                                     ->take(5)
                                     ->get(),
            'most_liked' => User::join('confessions', 'users.id', '=', 'confessions.user_id')
                               ->select('users.*', DB::raw('SUM(confessions.likes_count) as total_likes'))
                               ->groupBy('users.id')
                               ->orderByDesc('total_likes')
                               ->take(5)
                               ->get(),
            'most_active' => User::where('last_active_at', '>=', Carbon::now()->subWeek())
                                ->orderByDesc('last_active_at')
                                ->take(5)
                                ->get()
        ];
    }

    public function systemHealth()
    {
        $health = [
            'database' => $this->checkDatabaseHealth(),
            'storage' => $this->checkStorageHealth(),
            'cache' => $this->checkCacheHealth(),
            'queue' => $this->checkQueueHealth(),
        ];

        return response()->json($health);
    }

    private function checkDatabaseHealth()
    {
        try {
            DB::connection()->getPdo();
            return ['status' => 'healthy', 'message' => 'Database connection successful'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'Database connection failed'];
        }
    }

    private function checkStorageHealth()
    {
        $diskSpace = disk_free_space(storage_path());
        $totalSpace = disk_total_space(storage_path());
        $usagePercent = (($totalSpace - $diskSpace) / $totalSpace) * 100;

        return [
            'status' => $usagePercent > 90 ? 'warning' : 'healthy',
            'usage_percent' => round($usagePercent, 2),
            'free_space' => $this->formatBytes($diskSpace),
            'total_space' => $this->formatBytes($totalSpace)
        ];
    }

    private function checkCacheHealth()
    {
        try {
            cache()->put('health_check', 'test', 60);
            $value = cache()->get('health_check');
            return ['status' => $value === 'test' ? 'healthy' : 'error'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'Cache not working'];
        }
    }

    private function checkQueueHealth()
    {
        // Basic queue health check - can be enhanced based on your queue driver
        return ['status' => 'healthy', 'message' => 'Queue system operational'];
    }

    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
