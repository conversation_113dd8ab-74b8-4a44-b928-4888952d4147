<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Confession;
use App\Models\Report;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $query = User::withCount(['confessions', 'likes', 'bookmarks']);
        
        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('email', 'LIKE', "%{$search}%");
            });
        }
        
        // Filter by role
        if ($request->filled('role')) {
            $query->where('user_role', $request->role);
        }
        
        // Filter by status
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'banned':
                    $query->where('is_banned', true);
                    break;
                case 'verified':
                    $query->where('is_verified', true);
                    break;
                case 'active':
                    $query->where('last_active_at', '>=', Carbon::now()->subDays(7));
                    break;
                case 'inactive':
                    $query->where('last_active_at', '<', Carbon::now()->subDays(30))
                          ->orWhereNull('last_active_at');
                    break;
            }
        }
        
        // Sort options
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');
        
        if (in_array($sortBy, ['created_at', 'last_active_at', 'confessions_count', 'likes_count'])) {
            $query->orderBy($sortBy, $sortOrder);
        }
        
        $users = $query->paginate(20)->withQueryString();
        
        $stats = [
            'total' => User::count(),
            'banned' => User::where('is_banned', true)->count(),
            'verified' => User::where('is_verified', true)->count(),
            'active_week' => User::where('last_active_at', '>=', Carbon::now()->subWeek())->count(),
            'new_today' => User::whereDate('created_at', Carbon::today())->count(),
        ];
        
        return view('admin.users.index', compact('users', 'stats'));
    }
    
    public function show(User $user)
    {
        $user->load(['confessions', 'likes', 'bookmarks', 'friendships']);
        
        $stats = [
            'confessions_count' => $user->confessions()->count(),
            'ai_confessions' => $user->confessions()->where('is_ai_generated', true)->count(),
            'nsfw_confessions' => $user->confessions()->where('is_nsfw', true)->count(),
            'total_likes_received' => $user->confessions()->sum('likes_count'),
            'total_views' => $user->confessions()->sum('views'),
            'friends_count' => $user->friendships()->where('status', 'accepted')->count(),
            'messages_sent' => $user->sentMessages()->count(),
            'reports_made' => Report::where('user_id', $user->id)->count(),
            'reports_against' => Report::where('reportable_type', User::class)
                                      ->where('reportable_id', $user->id)->count(),
        ];
        
        $recentActivity = [
            'confessions' => $user->confessions()->latest()->take(5)->get(),
            'reports_made' => Report::where('user_id', $user->id)->with('reportable')->latest()->take(5)->get(),
            'reports_against' => Report::where('reportable_type', User::class)
                                      ->where('reportable_id', $user->id)
                                      ->with('user')->latest()->take(5)->get(),
        ];
        
        return view('admin.users.show', compact('user', 'stats', 'recentActivity'));
    }
    
    public function edit(User $user)
    {
        return view('admin.users.edit', compact('user'));
    }
    
    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'user_role' => 'required|in:user,moderator,admin,super_admin',
            'is_verified' => 'boolean',
            'admin_notes' => 'nullable|string'
        ]);
        
        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'user_role' => $request->user_role,
            'is_admin' => in_array($request->user_role, ['admin', 'super_admin']),
            'is_verified' => $request->boolean('is_verified'),
            'admin_notes' => $request->admin_notes ? json_encode([$request->admin_notes]) : null,
        ]);
        
        return redirect()->route('admin.users.show', $user)
                        ->with('success', 'User updated successfully.');
    }
    
    public function ban(Request $request, User $user)
    {
        $request->validate([
            'ban_reason' => 'required|string|max:500',
            'ban_duration' => 'nullable|integer|min:1|max:365', // days
        ]);
        
        $bannedUntil = $request->ban_duration 
                      ? Carbon::now()->addDays($request->ban_duration)
                      : null; // Permanent ban if no duration
        
        $user->update([
            'is_banned' => true,
            'banned_until' => $bannedUntil,
            'ban_reason' => $request->ban_reason,
        ]);
        
        return redirect()->route('admin.users.show', $user)
                        ->with('success', 'User banned successfully.');
    }
    
    public function unban(User $user)
    {
        $user->update([
            'is_banned' => false,
            'banned_until' => null,
            'ban_reason' => null,
        ]);
        
        return redirect()->route('admin.users.show', $user)
                        ->with('success', 'User unbanned successfully.');
    }
    
    public function verify(User $user)
    {
        $user->update(['is_verified' => true]);
        
        return redirect()->route('admin.users.show', $user)
                        ->with('success', 'User verified successfully.');
    }
    
    public function unverify(User $user)
    {
        $user->update(['is_verified' => false]);
        
        return redirect()->route('admin.users.show', $user)
                        ->with('success', 'User verification removed.');
    }
    
    public function destroy(User $user)
    {
        if ($user->is_admin) {
            return redirect()->back()->with('error', 'Cannot delete admin users.');
        }
        
        // Soft delete or anonymize user data
        $user->update([
            'name' => 'Deleted User',
            'email' => 'deleted_' . $user->id . '@deleted.com',
            'is_banned' => true,
            'ban_reason' => 'Account deleted by admin',
        ]);
        
        // Optionally anonymize confessions
        $user->confessions()->update([
            'is_anonymous' => true,
            'user_id' => null, // Or keep for admin tracking
        ]);
        
        return redirect()->route('admin.users.index')
                        ->with('success', 'User account deleted and data anonymized.');
    }
    
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:ban,unban,verify,unverify,delete',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'ban_reason' => 'required_if:action,ban|string|max:500',
            'ban_duration' => 'nullable|integer|min:1|max:365',
        ]);
        
        $users = User::whereIn('id', $request->user_ids)->get();
        $count = 0;
        
        foreach ($users as $user) {
            if ($user->is_admin && in_array($request->action, ['ban', 'delete'])) {
                continue; // Skip admin users for destructive actions
            }
            
            switch ($request->action) {
                case 'ban':
                    $bannedUntil = $request->ban_duration 
                                  ? Carbon::now()->addDays($request->ban_duration)
                                  : null;
                    $user->update([
                        'is_banned' => true,
                        'banned_until' => $bannedUntil,
                        'ban_reason' => $request->ban_reason,
                    ]);
                    $count++;
                    break;
                    
                case 'unban':
                    $user->update([
                        'is_banned' => false,
                        'banned_until' => null,
                        'ban_reason' => null,
                    ]);
                    $count++;
                    break;
                    
                case 'verify':
                    $user->update(['is_verified' => true]);
                    $count++;
                    break;
                    
                case 'unverify':
                    $user->update(['is_verified' => false]);
                    $count++;
                    break;
                    
                case 'delete':
                    $user->update([
                        'name' => 'Deleted User',
                        'email' => 'deleted_' . $user->id . '@deleted.com',
                        'is_banned' => true,
                        'ban_reason' => 'Account deleted by admin',
                    ]);
                    $count++;
                    break;
            }
        }
        
        return redirect()->route('admin.users.index')
                        ->with('success', "Bulk action completed on {$count} users.");
    }
}
