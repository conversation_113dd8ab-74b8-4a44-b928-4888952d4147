<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SecurityEvent;
use App\Models\ContentModeration;
use App\Models\User;
use Illuminate\Http\Request;
use Carbon\Carbon;

class SecurityController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    public function index()
    {
        $period = request('period', 'today');
        
        // Security metrics
        $securityMetrics = [
            'total_events' => SecurityEvent::count(),
            'critical_events' => SecurityEvent::where('severity', 'critical')->count(),
            'resolved_events' => SecurityEvent::where('status', 'resolved')->count(),
            'open_events' => SecurityEvent::where('status', 'open')->count(),
        ];

        $moderationStats = [
            'total_moderated' => ContentModeration::count(),
            'pending' => ContentModeration::where('status', 'pending')->count(),
            'approved' => ContentModeration::where('status', 'approved')->count(),
            'rejected' => ContentModeration::where('status', 'rejected')->count(),
        ];
        
        // Recent critical events
        $criticalEvents = SecurityEvent::critical()
                                     ->recent(24)
                                     ->with(['user', 'resolvedBy'])
                                     ->orderBy('created_at', 'desc')
                                     ->limit(10)
                                     ->get();

        // Pending moderation
        $pendingModeration = ContentModeration::pending()
                                            ->with(['user'])
                                            ->orderBy('ai_score', 'desc')
                                            ->limit(10)
                                            ->get();

        // Threat trends
        $threatTrends = SecurityEvent::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                                   ->where('created_at', '>=', now()->subDays(30))
                                   ->groupBy('date')
                                   ->orderBy('date')
                                   ->get();

        $topThreats = SecurityEvent::selectRaw('event_type, COUNT(*) as count')
                                 ->groupBy('event_type')
                                 ->orderBy('count', 'desc')
                                 ->limit(5)
                                 ->get();

        // Suspicious IPs
        $suspiciousIPs = SecurityEvent::selectRaw('ip_address, COUNT(*) as count')
                                    ->whereNotNull('ip_address')
                                    ->where('created_at', '>=', now()->subHours(24))
                                    ->groupBy('ip_address')
                                    ->having('count', '>=', 5)
                                    ->orderBy('count', 'desc')
                                    ->get();

        // Recent critical events and pending moderation
        $criticalEvents = SecurityEvent::where('severity', 'critical')
                                     ->where('created_at', '>=', now()->subHours(24))
                                     ->limit(5)
                                     ->get();

        $pendingModeration = ContentModeration::where('status', 'pending')
                                            ->limit(5)
                                            ->get();

        // Auto-moderation effectiveness
        $autoModerationStats = [
            'accuracy_rate' => $this->getAutoModerationAccuracy(),
            'false_positive_rate' => 5.2, // Using static value for now
            'content_blocked' => ContentModeration::where('auto_moderated', true)
                                                 ->where('status', ContentModeration::STATUS_REJECTED)
                                                 ->count(),
        ];

        return view('admin.security.index', compact(
            'securityMetrics',
            'moderationStats',
            'criticalEvents',
            'pendingModeration',
            'threatTrends',
            'topThreats',
            'suspiciousIPs',
            'autoModerationStats',
            'period'
        ));
    }

    public function events()
    {
        $events = SecurityEvent::with(['user', 'resolvedBy'])
                               ->orderBy('created_at', 'desc')
                               ->paginate(50);

        return view('admin.security.events', compact('events'));
    }

    public function moderation()
    {
        $moderations = ContentModeration::with(['user', 'reviewer'])
                                       ->orderBy('created_at', 'desc')
                                       ->paginate(50);

        return view('admin.security.moderation', compact('moderations'));
    }

    public function resolveEvent(Request $request, SecurityEvent $event)
    {
        $request->validate([
            'action_taken' => 'required|string|max:255',
        ]);

        $event->resolve($request->action_taken);

        return redirect()->back()->with('success', 'Security event resolved successfully.');
    }

    public function markFalsePositive(SecurityEvent $event)
    {
        $event->markFalsePositive();

        return redirect()->back()->with('success', 'Event marked as false positive.');
    }

    public function approveModerationContent(ContentModeration $moderation)
    {
        $moderation->approve();

        return redirect()->back()->with('success', 'Content approved successfully.');
    }

    public function rejectModerationContent(Request $request, ContentModeration $moderation)
    {
        $request->validate([
            'action' => 'required|in:warning,content_removed,user_suspended,user_banned',
            'reason' => 'required|string|max:500',
        ]);

        $moderation->takeAction($request->action, $request->reason);

        return redirect()->back()->with('success', 'Content moderation action taken successfully.');
    }

    public function escalateModeration(Request $request, ContentModeration $moderation)
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        $moderation->escalate($request->reason);

        return redirect()->back()->with('success', 'Content escalated for review.');
    }

    public function blockedIPs()
    {
        $blockedIPs = SecurityEvent::getSuspiciousIPs(24);
        
        return view('admin.security.blocked-ips', compact('blockedIPs'));
    }

    public function suspendedUsers()
    {
        $suspendedUsers = User::where('is_suspended', true)
                             ->orWhere('is_banned', true)
                             ->with(['securityEvents' => function($query) {
                                 $query->recent(30)->orderBy('created_at', 'desc');
                             }])
                             ->paginate(50);

        return view('admin.security.suspended-users', compact('suspendedUsers'));
    }

    public function unsuspendUser(User $user)
    {
        $user->update([
            'is_suspended' => false,
            'suspended_until' => null,
        ]);

        SecurityEvent::log(
            SecurityEvent::TYPE_SUSPICIOUS_ACTIVITY,
            SecurityEvent::SEVERITY_LOW,
            'User unsuspended by admin',
            $user->id,
            ['admin_id' => auth()->id()]
        );

        return redirect()->back()->with('success', 'User unsuspended successfully.');
    }

    public function unbanUser(User $user)
    {
        $user->update(['is_banned' => false]);

        SecurityEvent::log(
            SecurityEvent::TYPE_SUSPICIOUS_ACTIVITY,
            SecurityEvent::SEVERITY_LOW,
            'User unbanned by admin',
            $user->id,
            ['admin_id' => auth()->id()]
        );

        return redirect()->back()->with('success', 'User unbanned successfully.');
    }

    public function settings()
    {
        $settings = [
            'auto_moderation_enabled' => true,
            'ai_confidence_threshold' => 0.8,
            'max_failed_logins' => 5,
            'login_lockout_duration' => 30,
            'spam_detection_enabled' => true,
            'hate_speech_detection' => true,
            'personal_info_detection' => true,
            'adult_content_detection' => true,
            'rate_limiting_enabled' => true,
            'ip_blocking_enabled' => true,
        ];

        return view('admin.security.settings', compact('settings'));
    }

    public function updateSettings(Request $request)
    {
        $request->validate([
            'auto_moderation_enabled' => 'boolean',
            'ai_confidence_threshold' => 'numeric|min:0|max:1',
            'max_failed_logins' => 'integer|min:1|max:20',
            'login_lockout_duration' => 'integer|min:1|max:1440',
            'spam_detection_enabled' => 'boolean',
            'hate_speech_detection' => 'boolean',
            'personal_info_detection' => 'boolean',
            'adult_content_detection' => 'boolean',
            'rate_limiting_enabled' => 'boolean',
            'ip_blocking_enabled' => 'boolean',
        ]);

        // Save settings (implement based on your settings storage)
        // For now, we'll just redirect back with success

        return redirect()->back()->with('success', 'Security settings updated successfully.');
    }

    public function analytics()
    {
        $period = request('period', 'month');
        
        // Security analytics
        $analytics = [
            'threat_detection_rate' => $this->getThreatDetectionRate($period),
            'false_positive_rate' => $this->getFalsePositiveRate($period),
            'response_time' => $this->getAverageResponseTime($period),
            'blocked_attacks' => $this->getBlockedAttacks($period),
            'user_reports' => $this->getUserReports($period),
        ];

        // Charts data
        $securityTrends = SecurityEvent::getThreatTrends(30);
        $moderationTrends = $this->getModerationTrends(30);
        $riskDistribution = $this->getRiskDistribution($period);

        return view('admin.security.analytics', compact(
            'analytics',
            'securityTrends',
            'moderationTrends',
            'riskDistribution',
            'period'
        ));
    }

    public function export(Request $request)
    {
        $type = $request->get('type', 'security_events');
        $period = $request->get('period', 'month');
        $format = $request->get('format', 'csv');

        $data = $this->getExportData($type, $period);
        
        if ($format === 'csv') {
            return $this->exportToCsv($data, $type);
        } elseif ($format === 'pdf') {
            return $this->exportToPdf($data, $type);
        }

        return redirect()->back()->with('error', 'Invalid export format');
    }

    // Helper methods
    private function getAutoModerationAccuracy()
    {
        $total = ContentModeration::where('auto_moderated', true)->count();
        $correct = ContentModeration::where('auto_moderated', true)
                                  ->where('status', '!=', ContentModeration::STATUS_ESCALATED)
                                  ->count();

        return $total > 0 ? ($correct / $total) * 100 : 0;
    }

    private function getFalsePositiveRate($period = null)
    {
        $query = SecurityEvent::query();
        
        if ($period) {
            if ($period === 'today') {
                $query = $query->whereDate('created_at', Carbon::today());
            } elseif ($period === 'week') {
                $query = $query->where('created_at', '>=', Carbon::now()->subWeek());
            } elseif ($period === 'month') {
                $query = $query->where('created_at', '>=', Carbon::now()->subMonth());
            }
        }

        $total = $query->count();
        $falsePositives = $query->where('status', SecurityEvent::STATUS_FALSE_POSITIVE)->count();

        return $total > 0 ? ($falsePositives / $total) * 100 : 0;
    }

    private function getThreatDetectionRate($period)
    {
        return 95.5; // Placeholder
    }

    private function getAverageResponseTime($period)
    {
        return 15; // minutes - placeholder
    }

    private function getBlockedAttacks($period)
    {
        return SecurityEvent::where('created_at', '>=', Carbon::now()->subMonth())->count();
    }

    private function getUserReports($period)
    {
        return 0; // Placeholder - implement user reporting system
    }

    private function getModerationTrends($days)
    {
        return ContentModeration::where('created_at', '>=', Carbon::now()->subDays($days))
                                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                                ->groupBy('date')
                                ->orderBy('date')
                                ->get();
    }

    private function getRiskDistribution($period)
    {
        return SecurityEvent::selectRaw('severity, COUNT(*) as count')
                           ->groupBy('severity')
                           ->get();
    }

    private function getExportData($type, $period)
    {
        return []; // Placeholder
    }

    private function exportToCsv($data, $type)
    {
        // Placeholder
    }

    private function exportToPdf($data, $type)
    {
        // Placeholder
    }
}
