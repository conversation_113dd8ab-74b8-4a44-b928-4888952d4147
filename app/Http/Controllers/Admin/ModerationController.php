<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Confession;
use App\Models\Report;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ModerationController extends Controller
{
    public function reports(Request $request)
    {
        $query = Report::with(['user', 'reportable', 'resolvedBy']);
        
        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        } else {
            $query->where('status', '!=', Report::STATUS_RESOLVED);
        }
        
        // Filter by severity
        if ($request->filled('severity')) {
            $query->where('severity', $request->severity);
        }
        
        // Filter by type
        if ($request->filled('type')) {
            $query->where('reportable_type', $request->type);
        }
        
        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('reason', 'LIKE', "%{$search}%")
                  ->orWhere('description', 'LIKE', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'LIKE', "%{$search}%");
                  });
            });
        }
        
        $reports = $query->orderByDesc('created_at')->paginate(20)->withQueryString();
        
        $stats = [
            'pending' => Report::pending()->count(),
            'reviewing' => Report::reviewing()->count(),
            'resolved_today' => Report::resolved()->whereDate('resolved_at', today())->count(),
            'high_priority' => Report::highPriority()->where('status', '!=', Report::STATUS_RESOLVED)->count(),
        ];
        
        return view('admin.moderation.reports', compact('reports', 'stats'));
    }
    
    public function showReport(Report $report)
    {
        $report->load(['user', 'reportable', 'resolvedBy']);
        
        // Get related reports for the same content/user
        $relatedReports = collect();
        
        if ($report->reportable) {
            $relatedReports = Report::where('reportable_type', $report->reportable_type)
                                   ->where('reportable_id', $report->reportable_id)
                                   ->where('id', '!=', $report->id)
                                   ->with(['user'])
                                   ->get();
        }
        
        return view('admin.moderation.report-detail', compact('report', 'relatedReports'));
    }
    
    public function updateReport(Request $request, Report $report)
    {
        $request->validate([
            'status' => 'required|in:' . implode(',', [
                Report::STATUS_PENDING,
                Report::STATUS_REVIEWING,
                Report::STATUS_RESOLVED,
                Report::STATUS_DISMISSED
            ]),
            'admin_notes' => 'nullable|string|max:1000',
            'severity' => 'nullable|in:' . implode(',', [
                Report::SEVERITY_LOW,
                Report::SEVERITY_MEDIUM,
                Report::SEVERITY_HIGH,
                Report::SEVERITY_CRITICAL
            ])
        ]);
        
        $updateData = [
            'status' => $request->status,
            'admin_notes' => $request->admin_notes,
        ];
        
        if ($request->filled('severity')) {
            $updateData['severity'] = $request->severity;
        }
        
        if (in_array($request->status, [Report::STATUS_RESOLVED, Report::STATUS_DISMISSED])) {
            $updateData['resolved_by'] = Auth::id();
            $updateData['resolved_at'] = now();
        }
        
        $report->update($updateData);
        
        return redirect()->route('admin.moderation.reports')
                        ->with('success', 'Report updated successfully.');
    }
    
    public function confessions(Request $request)
    {
        $query = Confession::with(['user', 'reports']);
        
        // Filter options
        if ($request->filled('filter')) {
            switch ($request->filter) {
                case 'reported':
                    $query->whereHas('reports', function($q) {
                        $q->where('status', '!=', Report::STATUS_RESOLVED);
                    });
                    break;
                case 'nsfw':
                    $query->where('is_nsfw', true);
                    break;
                case 'ai':
                    $query->where('is_ai_generated', true);
                    break;
                case 'flagged':
                    $query->where('is_flagged', true);
                    break;
                case 'high_engagement':
                    $query->where(function($q) {
                        $q->where('likes_count', '>', 100)
                          ->orWhere('views', '>', 1000);
                    });
                    break;
            }
        }
        
        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('content', 'LIKE', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'LIKE', "%{$search}%");
                  });
            });
        }
        
        $confessions = $query->orderByDesc('created_at')->paginate(20)->withQueryString();
        
        $stats = [
            'total' => Confession::count(),
            'reported' => Confession::whereHas('reports', function($q) {
                $q->where('status', '!=', Report::STATUS_RESOLVED);
            })->count(),
            'nsfw' => Confession::where('is_nsfw', true)->count(),
            'ai_generated' => Confession::where('is_ai_generated', true)->count(),
            'flagged' => Confession::where('is_flagged', true)->count(),
        ];
        
        return view('admin.moderation.confessions', compact('confessions', 'stats'));
    }
    
    public function showConfession(Confession $confession)
    {
        $confession->load(['user', 'reports.user', 'likes', 'bookmarks']);
        
        $stats = [
            'reports_count' => $confession->reports()->count(),
            'pending_reports' => $confession->reports()->pending()->count(),
            'engagement_score' => $confession->getEngagementScore(),
        ];
        
        return view('admin.moderation.confession-detail', compact('confession', 'stats'));
    }
    
    public function updateConfession(Request $request, Confession $confession)
    {
        $request->validate([
            'action' => 'required|in:approve,flag,hide,delete,mark_nsfw,unmark_nsfw',
            'reason' => 'nullable|string|max:500'
        ]);
        
        switch ($request->action) {
            case 'approve':
                $confession->update(['is_flagged' => false]);
                // Resolve related reports
                $confession->reports()->pending()->update([
                    'status' => Report::STATUS_DISMISSED,
                    'resolved_by' => Auth::id(),
                    'resolved_at' => now(),
                    'admin_notes' => 'Content approved by moderator'
                ]);
                $message = 'Confession approved and reports dismissed.';
                break;
                
            case 'flag':
                $confession->update(['is_flagged' => true]);
                $message = 'Confession flagged for review.';
                break;
                
            case 'hide':
                $confession->update(['is_hidden' => true]);
                $confession->reports()->pending()->update([
                    'status' => Report::STATUS_RESOLVED,
                    'resolved_by' => Auth::id(),
                    'resolved_at' => now(),
                    'admin_notes' => $request->reason ?: 'Content hidden by moderator'
                ]);
                $message = 'Confession hidden from public view.';
                break;
                
            case 'delete':
                $confession->reports()->pending()->update([
                    'status' => Report::STATUS_RESOLVED,
                    'resolved_by' => Auth::id(),
                    'resolved_at' => now(),
                    'admin_notes' => $request->reason ?: 'Content deleted by moderator'
                ]);
                $confession->delete();
                return redirect()->route('admin.moderation.confessions')
                                ->with('success', 'Confession deleted successfully.');
                
            case 'mark_nsfw':
                $confession->update(['is_nsfw' => true]);
                $message = 'Confession marked as NSFW.';
                break;
                
            case 'unmark_nsfw':
                $confession->update(['is_nsfw' => false]);
                $message = 'NSFW marking removed from confession.';
                break;
        }
        
        return redirect()->route('admin.moderation.confession', $confession)
                        ->with('success', $message);
    }
    
    public function bulkModeration(Request $request)
    {
        $request->validate([
            'action' => 'required|in:approve,flag,hide,delete,mark_nsfw',
            'confession_ids' => 'required|array',
            'confession_ids.*' => 'exists:confessions,id',
            'reason' => 'nullable|string|max:500'
        ]);
        
        $confessions = Confession::whereIn('id', $request->confession_ids)->get();
        $count = 0;
        
        foreach ($confessions as $confession) {
            switch ($request->action) {
                case 'approve':
                    $confession->update(['is_flagged' => false]);
                    $confession->reports()->pending()->update([
                        'status' => Report::STATUS_DISMISSED,
                        'resolved_by' => Auth::id(),
                        'resolved_at' => now(),
                    ]);
                    break;
                    
                case 'flag':
                    $confession->update(['is_flagged' => true]);
                    break;
                    
                case 'hide':
                    $confession->update(['is_hidden' => true]);
                    $confession->reports()->pending()->update([
                        'status' => Report::STATUS_RESOLVED,
                        'resolved_by' => Auth::id(),
                        'resolved_at' => now(),
                    ]);
                    break;
                    
                case 'delete':
                    // Log the deletion for audit trail
                    \App\Models\SecurityEvent::logEvent(
                        'content_deleted',
                        'high',
                        'Admin deleted confession ID: ' . $confession->id,
                        Auth::id(),
                        request()->ip(),
                        [
                            'confession_id' => $confession->id,
                            'content_preview' => substr($confession->content, 0, 100),
                            'user_id' => $confession->user_id,
                            'reason' => $request->reason ?? 'Admin deletion'
                        ]
                    );

                    // Create content moderation record
                    \App\Models\ContentModeration::create([
                        'content_type' => 'confession',
                        'content_id' => $confession->id,
                        'user_id' => $confession->user_id,
                        'moderation_type' => 'admin_deletion',
                        'status' => 'rejected',
                        'reason' => $request->reason ?? 'Deleted by admin',
                        'action_taken' => 'content_removed',
                        'reviewed_by' => Auth::id(),
                        'reviewed_at' => now(),
                        'metadata' => [
                            'admin_name' => Auth::user()->name,
                            'deletion_reason' => $request->reason
                        ]
                    ]);

                    // Resolve related reports
                    $confession->reports()->pending()->update([
                        'status' => Report::STATUS_RESOLVED,
                        'resolved_by' => Auth::id(),
                        'resolved_at' => now(),
                        'admin_notes' => 'Content deleted by admin: ' . ($request->reason ?? 'No reason provided')
                    ]);

                    // Notify user if not anonymous
                    if ($confession->user_id && !$confession->is_anonymous) {
                        // You can implement notification system here
                        // For now, we'll just log it
                        \Log::info('User notification: Confession deleted', [
                            'user_id' => $confession->user_id,
                            'confession_id' => $confession->id,
                            'reason' => $request->reason
                        ]);
                    }

                    $confession->delete();
                    break;
                    
                case 'mark_nsfw':
                    $confession->update(['is_nsfw' => true]);
                    break;
            }
            $count++;
        }
        
        return redirect()->route('admin.moderation.confessions')
                        ->with('success', "Bulk action completed on {$count} confessions.");
    }
}
