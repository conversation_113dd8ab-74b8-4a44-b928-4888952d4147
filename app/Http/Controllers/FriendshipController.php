<?php

namespace App\Http\Controllers;

use App\Models\Friendship;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FriendshipController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $friends = Auth::user()->friends()->paginate(20);
        $pendingRequests = Auth::user()->friendRequests()->with('user')->get();
        $sentRequests = Auth::user()->friendships()->pending()->with('friend')->get();

        return view('friendships.index', compact('friends', 'pendingRequests', 'sentRequests'));
    }

    public function sendRequest(User $user)
    {
        if ($user->id === Auth::id()) {
            return back()->with('error', 'You cannot send a friend request to yourself!');
        }

        if (Auth::user()->isFriendsWith($user)) {
            return back()->with('error', 'You are already friends with this user!');
        }

        if (Auth::user()->hasSentFriendRequestTo($user)) {
            return back()->with('error', 'Friend request already sent!');
        }

        Friendship::create([
            'user_id' => Auth::id(),
            'friend_id' => $user->id,
            'status' => Friendship::STATUS_PENDING,
            'requested_at' => now()
        ]);

        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Friend request sent!'
            ]);
        }

        return back()->with('success', 'Friend request sent to ' . $user->name);
    }

    public function acceptRequest(Friendship $friendship)
    {
        if ($friendship->friend_id !== Auth::id()) {
            abort(403);
        }

        $friendship->accept();

        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Friend request accepted!'
            ]);
        }

        return back()->with('success', 'Friend request accepted!');
    }

    public function rejectRequest(Friendship $friendship)
    {
        if ($friendship->friend_id !== Auth::id()) {
            abort(403);
        }

        $friendship->reject();

        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Friend request rejected!'
            ]);
        }

        return back()->with('success', 'Friend request rejected!');
    }

    public function removeFriend(User $user)
    {
        // Remove both friendship records
        Friendship::where(function ($query) use ($user) {
            $query->where('user_id', Auth::id())->where('friend_id', $user->id);
        })->orWhere(function ($query) use ($user) {
            $query->where('user_id', $user->id)->where('friend_id', Auth::id());
        })->delete();

        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Friend removed!'
            ]);
        }

        return back()->with('success', 'Removed ' . $user->name . ' from friends');
    }
}
