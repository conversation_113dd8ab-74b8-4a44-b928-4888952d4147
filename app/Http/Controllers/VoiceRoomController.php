<?php

namespace App\Http\Controllers;

use App\Models\VoiceRoom;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class VoiceRoomController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth')->except(['index', 'show']);
    }

    public function index()
    {
        $rooms = VoiceRoom::with('host')
            ->where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->paginate(12);
            
        return view('voice-rooms.index', compact('rooms'));
    }

    public function create()
    {
        return view('voice-rooms.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'category' => 'nullable|string|max:50',
            'max_participants' => 'integer|min:2|max:50',
            'is_private' => 'boolean'
        ]);

        $room = VoiceRoom::create([
            'name' => $request->name,
            'description' => $request->description,
            'host_id' => Auth::id(),
            'category' => $request->category,
            'max_participants' => $request->max_participants ?? 10,
            'is_private' => $request->boolean('is_private'),
            'current_participants' => 1
        ]);

        return redirect()->route('voice-rooms.show', $room)
            ->with('success', 'Voice room created successfully!');
    }

    public function show(VoiceRoom $voiceRoom)
    {
        $voiceRoom->load(['host', 'activeParticipants.user']);

        // Get current user's participant record if they're in the room
        $currentParticipant = null;
        if (Auth::check()) {
            $currentParticipant = $voiceRoom->getParticipant(Auth::user());
        }

        return view('voice-rooms.show', compact('voiceRoom', 'currentParticipant'));
    }

    public function edit(VoiceRoom $voiceRoom)
    {
        if ($voiceRoom->host_id !== Auth::id()) {
            abort(403);
        }
        
        return view('voice-rooms.edit', compact('voiceRoom'));
    }

    public function update(Request $request, VoiceRoom $voiceRoom)
    {
        if ($voiceRoom->host_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'category' => 'nullable|string|max:50',
            'max_participants' => 'integer|min:2|max:50'
        ]);

        $voiceRoom->update([
            'name' => $request->name,
            'description' => $request->description,
            'category' => $request->category,
            'max_participants' => $request->max_participants
        ]);

        return redirect()->route('voice-rooms.show', $voiceRoom)
            ->with('success', 'Voice room updated successfully!');
    }

    public function destroy(VoiceRoom $voiceRoom)
    {
        if ($voiceRoom->host_id !== Auth::id()) {
            abort(403);
        }

        $voiceRoom->update(['is_active' => false]);
        
        return redirect()->route('voice-rooms.index')
            ->with('success', 'Voice room ended successfully!');
    }

    /**
     * Join a voice room
     */
    public function join(VoiceRoom $voiceRoom)
    {
        if (!$voiceRoom->is_active) {
            return response()->json(['success' => false, 'message' => 'This room is no longer active.'], 400);
        }

        if ($voiceRoom->isFull()) {
            return response()->json(['success' => false, 'message' => 'This room is full.'], 400);
        }

        if ($voiceRoom->hasParticipant(Auth::user())) {
            return response()->json(['success' => false, 'message' => 'You are already in this room.'], 400);
        }

        $voiceRoom->addParticipant(Auth::user());

        // Create system message for user joining
        \App\Models\VoiceRoomMessage::createSystemMessage(
            $voiceRoom->id,
            Auth::user()->name . ' joined the room',
            ['user_id' => Auth::id(), 'action' => 'joined']
        );

        // Log analytics
        \App\Models\Analytics::logEvent('voice_room_joined', Auth::id(), [
            'room_id' => $voiceRoom->id,
            'room_name' => $voiceRoom->name
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Successfully joined the room!',
            'participants_count' => $voiceRoom->current_participants
        ]);
    }

    /**
     * Leave a voice room
     */
    public function leave(VoiceRoom $voiceRoom)
    {
        if (!$voiceRoom->hasParticipant(Auth::user())) {
            return response()->json(['success' => false, 'message' => 'You are not in this room.'], 400);
        }

        // Create system message for user leaving
        \App\Models\VoiceRoomMessage::createSystemMessage(
            $voiceRoom->id,
            Auth::user()->name . ' left the room',
            ['user_id' => Auth::id(), 'action' => 'left']
        );

        $voiceRoom->removeParticipant(Auth::user());

        // Log analytics
        \App\Models\Analytics::logEvent('voice_room_left', Auth::id(), [
            'room_id' => $voiceRoom->id,
            'room_name' => $voiceRoom->name
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Successfully left the room.',
            'participants_count' => $voiceRoom->current_participants
        ]);
    }

    /**
     * Toggle self mute status
     */
    public function toggleMute(VoiceRoom $voiceRoom)
    {
        $participant = $voiceRoom->getParticipant(Auth::user());

        if (!$participant) {
            return response()->json(['success' => false, 'message' => 'You are not in this room.'], 400);
        }

        $participant->update([
            'is_self_muted' => !$participant->is_self_muted
        ]);

        return response()->json([
            'success' => true,
            'is_muted' => $participant->is_self_muted,
            'message' => $participant->is_self_muted ? 'Microphone muted' : 'Microphone unmuted'
        ]);
    }

    /**
     * Host mute/unmute a participant
     */
    public function muteParticipant(VoiceRoom $voiceRoom, \App\Models\User $user)
    {
        // Check if current user is host or moderator
        $currentParticipant = $voiceRoom->getParticipant(Auth::user());
        if (!$currentParticipant || !$currentParticipant->isModerator()) {
            return response()->json(['success' => false, 'message' => 'You do not have permission to mute participants.'], 403);
        }

        $targetParticipant = $voiceRoom->getParticipant($user);
        if (!$targetParticipant) {
            return response()->json(['success' => false, 'message' => 'User is not in this room.'], 400);
        }

        $targetParticipant->update([
            'is_muted_by_host' => !$targetParticipant->is_muted_by_host
        ]);

        // Log the action
        \App\Models\Analytics::logEvent('participant_muted', Auth::id(), [
            'room_id' => $voiceRoom->id,
            'target_user_id' => $user->id,
            'action' => $targetParticipant->is_muted_by_host ? 'muted' : 'unmuted'
        ]);

        return response()->json([
            'success' => true,
            'is_muted' => $targetParticipant->is_muted_by_host,
            'message' => $targetParticipant->is_muted_by_host ?
                "Muted {$user->name}" : "Unmuted {$user->name}"
        ]);
    }

    /**
     * Kick a participant from the room
     */
    public function kickParticipant(VoiceRoom $voiceRoom, \App\Models\User $user)
    {
        // Check if current user is host or moderator
        $currentParticipant = $voiceRoom->getParticipant(Auth::user());
        if (!$currentParticipant || !$currentParticipant->isModerator()) {
            return response()->json(['success' => false, 'message' => 'You do not have permission to kick participants.'], 403);
        }

        $targetParticipant = $voiceRoom->getParticipant($user);
        if (!$targetParticipant) {
            return response()->json(['success' => false, 'message' => 'User is not in this room.'], 400);
        }

        // Cannot kick the host
        if ($targetParticipant->isHost()) {
            return response()->json(['success' => false, 'message' => 'Cannot kick the room host.'], 400);
        }

        $voiceRoom->removeParticipant($user);

        // Log the action
        \App\Models\Analytics::logEvent('participant_kicked', Auth::id(), [
            'room_id' => $voiceRoom->id,
            'target_user_id' => $user->id,
            'target_user_name' => $user->name
        ]);

        return response()->json([
            'success' => true,
            'message' => "Kicked {$user->name} from the room",
            'participants_count' => $voiceRoom->current_participants
        ]);
    }

    /**
     * Get friends who can be invited to the room
     */
    public function getInvitableFriends(VoiceRoom $voiceRoom)
    {
        $user = Auth::user();

        // Get user's friends (people they follow who also follow them back)
        $friends = $user->following()
            ->whereHas('followers', function($query) use ($user) {
                $query->where('follower_id', $user->id);
            })
            ->whereNotIn('users.id', function($query) use ($voiceRoom) {
                $query->select('user_id')
                      ->from('voice_room_participants')
                      ->where('voice_room_id', $voiceRoom->id)
                      ->whereNull('left_at');
            })
            ->select('users.id', 'users.name', 'users.username', 'users.avatar')
            ->get();

        return response()->json([
            'success' => true,
            'friends' => $friends
        ]);
    }

    /**
     * Invite friends to the voice room
     */
    public function inviteFriends(VoiceRoom $voiceRoom, Request $request)
    {
        $request->validate([
            'friend_ids' => 'required|array',
            'friend_ids.*' => 'exists:users,id'
        ]);

        $user = Auth::user();
        $invitedCount = 0;
        $errors = [];

        foreach ($request->friend_ids as $friendId) {
            $friend = \App\Models\User::find($friendId);

            // Check if they're actually friends
            $isFriend = $user->following()
                ->where('users.id', $friendId)
                ->whereHas('followers', function($query) use ($user) {
                    $query->where('follower_id', $user->id);
                })
                ->exists();

            if (!$isFriend) {
                $errors[] = "You are not friends with {$friend->name}";
                continue;
            }

            // Check if friend is already in the room
            if ($voiceRoom->hasParticipant($friend)) {
                $errors[] = "{$friend->name} is already in the room";
                continue;
            }

            // Create notification for the friend
            \App\Models\Notification::create([
                'user_id' => $friendId,
                'type' => 'voice_room_invitation',
                'title' => 'Voice Room Invitation',
                'message' => "{$user->name} invited you to join '{$voiceRoom->name}'",
                'data' => json_encode([
                    'room_id' => $voiceRoom->id,
                    'room_name' => $voiceRoom->name,
                    'inviter_id' => $user->id,
                    'inviter_name' => $user->name
                ])
            ]);

            $invitedCount++;
        }

        // Log analytics
        if ($invitedCount > 0) {
            \App\Models\Analytics::logEvent('voice_room_invitations_sent', $user->id, [
                'room_id' => $voiceRoom->id,
                'invited_count' => $invitedCount,
                'friend_ids' => $request->friend_ids
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => "Invited {$invitedCount} friends to the room",
            'invited_count' => $invitedCount,
            'errors' => $errors
        ]);
    }

    /**
     * Get participants with friend status
     */
    public function getParticipants(VoiceRoom $voiceRoom)
    {
        $user = Auth::user();
        $participants = $voiceRoom->activeParticipants()
            ->with('user')
            ->get()
            ->map(function($participant) use ($user) {
                $participantData = [
                    'id' => $participant->id,
                    'user_id' => $participant->user_id,
                    'name' => $participant->user->name,
                    'username' => $participant->user->username,
                    'avatar' => $participant->user->avatar,
                    'status' => $participant->getDisplayStatus(),
                    'role' => $participant->role,
                    'is_speaking' => $participant->is_speaking,
                    'is_muted' => $participant->isMuted(),
                    'joined_at' => $participant->joined_at->diffForHumans(),
                    'is_friend' => false,
                    'is_verified' => $participant->user->is_verified ?? false
                ];

                // Check if this participant is a friend
                if ($user && $user->id !== $participant->user_id) {
                    $participantData['is_friend'] = $user->following()
                        ->where('users.id', $participant->user_id)
                        ->whereHas('followers', function($query) use ($user) {
                            $query->where('follower_id', $user->id);
                        })
                        ->exists();
                }

                return $participantData;
            });

        return response()->json([
            'success' => true,
            'participants' => $participants,
            'total_count' => $participants->count()
        ]);
    }

    /**
     * Send a chat message
     */
    public function sendMessage(VoiceRoom $voiceRoom, Request $request)
    {
        $user = Auth::user();

        // Check if user is a participant
        $participant = $voiceRoom->getParticipant($user);
        if (!$participant) {
            return response()->json([
                'success' => false,
                'message' => 'You must be a participant to send messages.'
            ], 403);
        }

        $request->validate([
            'message' => 'required|string|max:1000',
            'is_anonymous' => 'boolean'
        ]);

        $message = \App\Models\VoiceRoomMessage::create([
            'voice_room_id' => $voiceRoom->id,
            'user_id' => $user->id,
            'message' => $request->message,
            'message_type' => \App\Models\VoiceRoomMessage::TYPE_TEXT,
            'is_anonymous' => $request->boolean('is_anonymous', false)
        ]);

        // Log the activity
        \App\Models\AdminLog::create([
            'admin_id' => $user->id,
            'action' => 'voice_room_message_sent',
            'target_type' => 'VoiceRoom',
            'target_id' => $voiceRoom->id,
            'details' => [
                'message_id' => $message->id,
                'message_length' => strlen($request->message),
                'is_anonymous' => $request->boolean('is_anonymous', false)
            ]
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Message sent successfully',
            'message_data' => [
                'id' => $message->id,
                'message' => $message->getFormattedMessage(),
                'sender_name' => $message->getSenderName(),
                'sender_verified' => $message->user ? $message->user->is_verified : false,
                'message_type' => $message->message_type,
                'created_at' => $message->created_at->format('H:i'),
                'can_edit' => $message->canEdit($user),
                'can_delete' => $message->canDelete($user),
                'is_own_message' => true
            ]
        ]);
    }

    /**
     * Get chat messages
     */
    public function getMessages(VoiceRoom $voiceRoom, Request $request)
    {
        $user = Auth::user();

        // Check if user is a participant
        $participant = $voiceRoom->getParticipant($user);
        if (!$participant) {
            return response()->json([
                'success' => false,
                'message' => 'You must be a participant to view messages.'
            ], 403);
        }

        $lastMessageId = $request->get('last_message_id', 0);

        $messages = $voiceRoom->messages()
            ->with('user')
            ->where('id', '>', $lastMessageId)
            ->active()
            ->orderBy('created_at', 'asc')
            ->limit(50)
            ->get();

        $formattedMessages = $messages->map(function ($message) use ($user) {
            return [
                'id' => $message->id,
                'message' => $message->getFormattedMessage(),
                'sender_name' => $message->getSenderName(),
                'sender_verified' => $message->user ? $message->user->is_verified : false,
                'message_type' => $message->message_type,
                'created_at' => $message->created_at->format('H:i'),
                'can_edit' => $message->canEdit($user),
                'can_delete' => $message->canDelete($user),
                'is_own_message' => $message->user_id === $user->id
            ];
        });

        return response()->json([
            'success' => true,
            'messages' => $formattedMessages,
            'last_message_id' => $messages->last() ? $messages->last()->id : $lastMessageId
        ]);
    }

    /**
     * Delete a chat message
     */
    public function deleteMessage(VoiceRoom $voiceRoom, \App\Models\VoiceRoomMessage $message)
    {
        $user = Auth::user();

        // Check if user can delete the message
        if (!$message->canDelete($user)) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to delete this message.'
            ], 403);
        }

        $message->update([
            'is_deleted' => true,
            'message' => '[Message deleted]'
        ]);

        // Log the activity
        \App\Models\AdminLog::create([
            'admin_id' => $user->id,
            'action' => 'voice_room_message_deleted',
            'target_type' => 'VoiceRoomMessage',
            'target_id' => $message->id,
            'details' => [
                'voice_room_id' => $voiceRoom->id,
                'original_sender_id' => $message->user_id
            ]
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Message deleted successfully'
        ]);
    }

    /**
     * Edit a chat message
     */
    public function editMessage(VoiceRoom $voiceRoom, \App\Models\VoiceRoomMessage $message, Request $request)
    {
        $user = Auth::user();

        // Check if user can edit the message
        if (!$message->canEdit($user)) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot edit this message.'
            ], 403);
        }

        $request->validate([
            'message' => 'required|string|max:1000'
        ]);

        $originalMessage = $message->message;

        $message->update([
            'message' => $request->message,
            'edited_at' => now()
        ]);

        // Log the activity
        \App\Models\AdminLog::create([
            'admin_id' => $user->id,
            'action' => 'voice_room_message_edited',
            'target_type' => 'VoiceRoomMessage',
            'target_id' => $message->id,
            'details' => [
                'voice_room_id' => $voiceRoom->id,
                'original_message' => $originalMessage,
                'new_message' => $request->message
            ]
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Message updated successfully',
            'message_data' => [
                'id' => $message->id,
                'message' => $message->getFormattedMessage(),
                'edited_at' => $message->edited_at->format('H:i')
            ]
        ]);
    }
}
