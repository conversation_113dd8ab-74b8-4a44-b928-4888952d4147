<?php

namespace App\Http\Controllers;

use App\Models\VoiceRoom;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class VoiceRoomController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth')->except(['index', 'show']);
    }

    public function index()
    {
        $rooms = VoiceRoom::with('host')
            ->where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->paginate(12);
            
        return view('voice-rooms.index', compact('rooms'));
    }

    public function create()
    {
        return view('voice-rooms.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'category' => 'nullable|string|max:50',
            'max_participants' => 'integer|min:2|max:50',
            'is_private' => 'boolean'
        ]);

        $room = VoiceRoom::create([
            'name' => $request->name,
            'description' => $request->description,
            'host_id' => Auth::id(),
            'category' => $request->category,
            'max_participants' => $request->max_participants ?? 10,
            'is_private' => $request->boolean('is_private'),
            'current_participants' => 1
        ]);

        return redirect()->route('voice-rooms.show', $room)
            ->with('success', 'Voice room created successfully!');
    }

    public function show(VoiceRoom $voiceRoom)
    {
        $voiceRoom->load('host');
        return view('voice-rooms.show', compact('voiceRoom'));
    }

    public function edit(VoiceRoom $voiceRoom)
    {
        if ($voiceRoom->host_id !== Auth::id()) {
            abort(403);
        }
        
        return view('voice-rooms.edit', compact('voiceRoom'));
    }

    public function update(Request $request, VoiceRoom $voiceRoom)
    {
        if ($voiceRoom->host_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:500',
            'category' => 'nullable|string|max:50',
            'max_participants' => 'integer|min:2|max:50'
        ]);

        $voiceRoom->update([
            'name' => $request->name,
            'description' => $request->description,
            'category' => $request->category,
            'max_participants' => $request->max_participants
        ]);

        return redirect()->route('voice-rooms.show', $voiceRoom)
            ->with('success', 'Voice room updated successfully!');
    }

    public function destroy(VoiceRoom $voiceRoom)
    {
        if ($voiceRoom->host_id !== Auth::id()) {
            abort(403);
        }

        $voiceRoom->update(['is_active' => false]);
        
        return redirect()->route('voice-rooms.index')
            ->with('success', 'Voice room ended successfully!');
    }
}
