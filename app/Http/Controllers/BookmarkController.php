<?php

namespace App\Http\Controllers;

use App\Models\Bookmark;
use App\Models\Confession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BookmarkController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index(Request $request)
    {
        $query = Auth::user()->bookmarks()->with('confession.user');

        if ($request->has('collection') && $request->collection) {
            $query->inCollection($request->collection);
        }

        $bookmarks = $query->orderBy('created_at', 'desc')->paginate(20);
        
        $collections = Auth::user()->bookmarks()
            ->select('collection_name')
            ->distinct()
            ->pluck('collection_name');

        return view('bookmarks.index', compact('bookmarks', 'collections'));
    }

    public function toggle(Request $request, Confession $confession)
    {
        $request->validate([
            'collection_name' => 'nullable|string|max:50'
        ]);

        $collectionName = $request->collection_name ?: 'default';

        $bookmark = Bookmark::where('user_id', Auth::id())
            ->where('confession_id', $confession->id)
            ->first();

        if ($bookmark) {
            $bookmark->delete();
            $confession->decrementBookmarks();
            $bookmarked = false;
            $message = 'Bookmark removed';
        } else {
            Bookmark::create([
                'user_id' => Auth::id(),
                'confession_id' => $confession->id,
                'collection_name' => $collectionName
            ]);
            $confession->incrementBookmarks();
            $bookmarked = true;
            $message = 'Confession bookmarked';
        }

        if ($request->ajax()) {
            return response()->json([
                'bookmarked' => $bookmarked,
                'message' => $message
            ]);
        }

        return back()->with('success', $message);
    }

    public function createCollection(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:50',
            'confession_ids' => 'array',
            'confession_ids.*' => 'exists:confessions,id'
        ]);

        foreach ($request->confession_ids as $confessionId) {
            Bookmark::updateOrCreate([
                'user_id' => Auth::id(),
                'confession_id' => $confessionId,
            ], [
                'collection_name' => $request->name
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Collection created successfully!'
        ]);
    }

    public function deleteCollection(Request $request)
    {
        $request->validate([
            'collection_name' => 'required|string'
        ]);

        Auth::user()->bookmarks()
            ->inCollection($request->collection_name)
            ->delete();

        return response()->json([
            'success' => true,
            'message' => 'Collection deleted successfully!'
        ]);
    }
}
