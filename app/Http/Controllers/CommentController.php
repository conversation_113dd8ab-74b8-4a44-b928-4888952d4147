<?php

namespace App\Http\Controllers;

use App\Models\Comment;
use App\Models\Confession;
use App\Notifications\ConfessionCommented;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CommentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function store(Request $request, Confession $confession)
    {
        $request->validate([
            'content' => 'required|string|max:500',
            'is_anonymous' => 'boolean'
        ]);

        Comment::create([
            'user_id' => Auth::id(),
            'confession_id' => $confession->id,
            'content' => $request->content,
            'is_anonymous' => $request->boolean('is_anonymous')
        ]);
        
        // Send notification
        if ($confession->user_id !== Auth::id()) {
            $confession->user->notify(new ConfessionCommented($confession, Auth::user()));
        }

        return back()->with('success', 'Comment added successfully!');
    }

    public function destroy(Comment $comment)
    {
        if ($comment->user_id !== Auth::id()) {
            abort(403);
        }

        $comment->delete();
        
        return back()->with('success', 'Comment deleted successfully!');
    }
}
