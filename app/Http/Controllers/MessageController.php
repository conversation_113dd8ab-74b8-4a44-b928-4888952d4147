<?php

namespace App\Http\Controllers;

use App\Models\Message;
use App\Models\User;
use App\Models\Confession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MessageController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $conversations = Message::where('sender_id', Auth::id())
            ->orWhere('receiver_id', Auth::id())
            ->with(['sender', 'receiver'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->groupBy(function ($message) {
                $otherUserId = $message->sender_id === Auth::id() 
                    ? $message->receiver_id 
                    : $message->sender_id;
                return $otherUserId;
            })
            ->map(function ($messages) {
                return $messages->first();
            });

        return view('messages.index', compact('conversations'));
    }

    public function show(User $user)
    {
        $messages = Message::betweenUsers(Auth::id(), $user->id)
            ->with(['sender', 'receiver', 'confession'])
            ->orderBy('created_at', 'asc')
            ->get();

        // Mark messages as read
        Message::where('sender_id', $user->id)
            ->where('receiver_id', Auth::id())
            ->unread()
            ->update(['is_read' => true]);

        return view('messages.show', compact('user', 'messages'));
    }

    public function store(Request $request, User $user)
    {
        $request->validate([
            'content' => 'required|string|max:1000',
            'confession_id' => 'nullable|exists:confessions,id'
        ]);

        $messageType = $request->confession_id ? 'confession_share' : 'text';

        $message = Message::create([
            'sender_id' => Auth::id(),
            'receiver_id' => $user->id,
            'content' => $request->content,
            'message_type' => $messageType,
            'confession_id' => $request->confession_id
        ]);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => $message->load(['sender', 'confession'])
            ]);
        }

        return back()->with('success', 'Message sent!');
    }

    public function shareConfession(Request $request, User $user, Confession $confession)
    {
        $request->validate([
            'message' => 'nullable|string|max:500'
        ]);

        $content = $request->message ?: "Check out this confession!";

        Message::create([
            'sender_id' => Auth::id(),
            'receiver_id' => $user->id,
            'content' => $content,
            'message_type' => 'confession_share',
            'confession_id' => $confession->id
        ]);

        return response()->json(['success' => true]);
    }

    public function getUnreadCount()
    {
        $count = Message::where('receiver_id', Auth::id())
            ->unread()
            ->count();

        return response()->json(['count' => $count]);
    }
}
