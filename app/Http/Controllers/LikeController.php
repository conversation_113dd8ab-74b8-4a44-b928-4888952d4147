<?php

namespace App\Http\Controllers;

use App\Models\Confession;
use App\Models\Like;
use App\Notifications\ConfessionLiked;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LikeController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function toggle(Confession $confession)
    {
        $like = Like::where('user_id', Auth::id())
            ->where('confession_id', $confession->id)
            ->first();

        if ($like) {
            $like->delete();
            $confession->decrement('likes_count');
            $liked = false;
        } else {
            Like::create([
                'user_id' => Auth::id(),
                'confession_id' => $confession->id
            ]);
            $confession->increment('likes_count');
            $liked = true;
            
            // Send notification
            if ($confession->user_id !== Auth::id()) {
                $confession->user->notify(new ConfessionLiked($confession, Auth::user()));
            }
        }

        if (request()->ajax()) {
            return response()->json([
                'liked' => $liked,
                'likes_count' => $confession->fresh()->likes_count
            ]);
        }

        return back();
    }
}
