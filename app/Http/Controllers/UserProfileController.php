<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;

class UserProfileController extends Controller
{
    public function show(User $user)
    {
        $confessions = $user->confessions()
            ->where('is_anonymous', false)
            ->with('likes', 'comments')
            ->orderBy('created_at', 'desc')
            ->paginate(10);
            
        $stats = [
            'confessions_count' => $user->confessions()->count(),
            'public_confessions' => $user->confessions()->where('is_anonymous', false)->count(),
            'total_likes' => $user->confessions()->sum('likes_count'),
            'followers_count' => $user->followers()->count(),
            'following_count' => $user->following()->count(),
        ];
        
        return view('users.profile', compact('user', 'confessions', 'stats'));
    }
}