<?php

namespace App\Http\Controllers;

use App\Models\Follow;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FollowController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function toggle(User $user)
    {
        if ($user->id === Auth::id()) {
            return back()->with('error', 'You cannot follow yourself!');
        }

        $follow = Follow::where('follower_id', Auth::id())
            ->where('following_id', $user->id)
            ->first();

        if ($follow) {
            $follow->delete();
            $followed = false;
            $message = 'Unfollowed ' . $user->name;
        } else {
            Follow::create([
                'follower_id' => Auth::id(),
                'following_id' => $user->id
            ]);
            $followed = true;
            $message = 'Now following ' . $user->name;
        }

        if (request()->ajax()) {
            return response()->json([
                'followed' => $followed,
                'message' => $message
            ]);
        }

        return back()->with('success', $message);
    }

    public function followers(User $user)
    {
        $followers = $user->followers()->with('follower')->paginate(20);
        return view('users.followers', compact('user', 'followers'));
    }

    public function following(User $user)
    {
        $following = $user->following()->with('following')->paginate(20);
        return view('users.following', compact('user', 'following'));
    }
}
