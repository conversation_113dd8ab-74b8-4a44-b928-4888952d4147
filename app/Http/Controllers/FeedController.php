<?php

namespace App\Http\Controllers;

use App\Models\Confession;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class FeedController extends Controller
{
    public function index(Request $request)
    {
        $filter = $request->get('filter', 'trending');
        $category = $request->get('category');
        $nsfw = $request->boolean('nsfw', false);
        
        $confessions = $this->getConfessionsByFilter($filter, $category, $nsfw);
        
        if ($request->ajax()) {
            return response()->json([
                'confessions' => $confessions->items(),
                'hasMore' => $confessions->hasMorePages(),
                'nextPage' => $confessions->currentPage() + 1
            ]);
        }
        
        return view('feed.index', compact('confessions', 'filter', 'category', 'nsfw'));
    }

    public function trending(Request $request)
    {
        $timeframe = $request->get('timeframe', 'week'); // day, week, month, all
        $category = $request->get('category');
        
        $confessions = $this->getTrendingConfessions($timeframe, $category);
        
        return view('feed.trending', compact('confessions', 'timeframe', 'category'));
    }

    public function wildest(Request $request)
    {
        $confessions = Confession::with(['user', 'likes', 'reactions'])
            ->nsfw()
            ->where('created_at', '>=', now()->subDays(30))
            ->orderByRaw('(likes_count + bookmark_count + share_count + views * 0.1) DESC')
            ->paginate(20);
            
        return view('feed.wildest', compact('confessions'));
    }

    public function categories()
    {
        $categories = [
            'wildest' => [
                'name' => 'Wildest',
                'icon' => '🔥',
                'description' => 'The most shocking and wild confessions',
                'nsfw' => true
            ],
            'nsfw' => [
                'name' => 'NSFW',
                'icon' => '🔞',
                'description' => 'Adult content and mature themes',
                'nsfw' => true
            ],
            'softcore' => [
                'name' => 'Softcore',
                'icon' => '💕',
                'description' => 'Romantic and sensual confessions',
                'nsfw' => false
            ],
            'fantasies' => [
                'name' => 'Fantasies',
                'icon' => '💭',
                'description' => 'Dreams, desires, and fantasies',
                'nsfw' => false
            ],
            'first-time' => [
                'name' => 'First Time',
                'icon' => '🌟',
                'description' => 'First experiences and new adventures',
                'nsfw' => false
            ],
            'cheating' => [
                'name' => 'Cheating',
                'icon' => '💔',
                'description' => 'Infidelity and relationship drama',
                'nsfw' => false
            ],
            'office' => [
                'name' => 'Office',
                'icon' => '💼',
                'description' => 'Workplace confessions and office drama',
                'nsfw' => false
            ],
            'college' => [
                'name' => 'College',
                'icon' => '🎓',
                'description' => 'University and college experiences',
                'nsfw' => false
            ],
            'travel' => [
                'name' => 'Travel',
                'icon' => '✈️',
                'description' => 'Adventures and experiences while traveling',
                'nsfw' => false
            ],
            'family' => [
                'name' => 'Family',
                'icon' => '👨‍👩‍👧‍👦',
                'description' => 'Family secrets and relationships',
                'nsfw' => false
            ]
        ];
        
        return view('feed.categories', compact('categories'));
    }

    public function category(Request $request, $category)
    {
        $sort = $request->get('sort', 'recent'); // recent, popular, trending
        $nsfw = $request->boolean('nsfw', false);
        
        $query = Confession::with(['user', 'likes', 'reactions'])
            ->where('category', $category);
            
        if ($nsfw) {
            $query->nsfw();
        } else {
            $query->sfw();
        }
        
        switch ($sort) {
            case 'popular':
                $query->orderBy('likes_count', 'desc');
                break;
            case 'trending':
                $query->trending();
                break;
            default:
                $query->orderBy('created_at', 'desc');
        }
        
        $confessions = $query->paginate(20);
        
        return view('feed.category', compact('confessions', 'category', 'sort', 'nsfw'));
    }

    public function search(Request $request)
    {
        $query = $request->get('q');
        $category = $request->get('category');
        $nsfw = $request->boolean('nsfw', false);
        $sort = $request->get('sort', 'relevance');
        
        if (!$query) {
            return redirect()->route('feed.index');
        }
        
        $confessions = $this->searchConfessions($query, $category, $nsfw, $sort);
        
        return view('feed.search', compact('confessions', 'query', 'category', 'nsfw', 'sort'));
    }

    public function forYou(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('feed.index');
        }
        
        $confessions = $this->getPersonalizedFeed(Auth::user());
        
        return view('feed.for-you', compact('confessions'));
    }

    private function getConfessionsByFilter($filter, $category = null, $nsfw = false)
    {
        $query = Confession::with(['user', 'likes', 'reactions']);
        
        // Apply category filter
        if ($category) {
            $query->where('category', $category);
        }
        
        // Apply NSFW filter
        if ($nsfw) {
            $query->nsfw();
        } else {
            $query->sfw();
        }
        
        // Apply main filter
        switch ($filter) {
            case 'trending':
                $query->trending();
                break;
            case 'popular':
                $query->orderBy('likes_count', 'desc');
                break;
            case 'wildest':
                $query->wildest();
                break;
            case 'recent':
                $query->orderBy('created_at', 'desc');
                break;
            case 'ai':
                $query->aiGenerated()->orderBy('created_at', 'desc');
                break;
            case 'user':
                $query->userGenerated()->orderBy('created_at', 'desc');
                break;
            default:
                $query->trending();
        }
        
        return $query->paginate(20);
    }

    private function getTrendingConfessions($timeframe, $category = null)
    {
        $query = Confession::with(['user', 'likes', 'reactions']);
        
        // Apply timeframe
        switch ($timeframe) {
            case 'day':
                $query->where('created_at', '>=', now()->subDay());
                break;
            case 'week':
                $query->where('created_at', '>=', now()->subWeek());
                break;
            case 'month':
                $query->where('created_at', '>=', now()->subMonth());
                break;
            // 'all' doesn't add a time constraint
        }
        
        if ($category) {
            $query->where('category', $category);
        }
        
        return $query->trending()->paginate(20);
    }

    private function searchConfessions($query, $category = null, $nsfw = false, $sort = 'relevance')
    {
        $confessions = Confession::with(['user', 'likes', 'reactions'])
            ->where('content', 'LIKE', "%{$query}%");
            
        if ($category) {
            $confessions->where('category', $category);
        }
        
        if ($nsfw) {
            $confessions->nsfw();
        } else {
            $confessions->sfw();
        }
        
        switch ($sort) {
            case 'recent':
                $confessions->orderBy('created_at', 'desc');
                break;
            case 'popular':
                $confessions->orderBy('likes_count', 'desc');
                break;
            case 'trending':
                $confessions->trending();
                break;
            default: // relevance
                $confessions->orderByRaw("
                    CASE 
                        WHEN content LIKE ? THEN 3
                        WHEN content LIKE ? THEN 2
                        ELSE 1
                    END DESC, likes_count DESC
                ", ["%{$query}%", "%{$query}%"]);
        }
        
        return $confessions->paginate(20);
    }

    private function getPersonalizedFeed(User $user)
    {
        // Get user's interaction history
        $likedCategories = $user->likes()
            ->join('confessions', 'likes.confession_id', '=', 'confessions.id')
            ->select('confessions.category')
            ->groupBy('confessions.category')
            ->orderByRaw('COUNT(*) DESC')
            ->limit(5)
            ->pluck('category')
            ->toArray();
            
        $bookmarkedCategories = $user->bookmarks()
            ->join('confessions', 'bookmarks.confession_id', '=', 'confessions.id')
            ->select('confessions.category')
            ->groupBy('confessions.category')
            ->orderByRaw('COUNT(*) DESC')
            ->limit(5)
            ->pluck('category')
            ->toArray();
            
        $preferredCategories = array_unique(array_merge($likedCategories, $bookmarkedCategories));
        
        $query = Confession::with(['user', 'likes', 'reactions']);
        
        if (!empty($preferredCategories)) {
            $query->whereIn('category', $preferredCategories);
        }
        
        // Mix of trending and recent content
        return $query->orderByRaw('
            (likes_count + bookmark_count + share_count) * 
            (CASE WHEN created_at >= ? THEN 2 ELSE 1 END) DESC
        ', [now()->subDays(3)])
        ->paginate(20);
    }
}
