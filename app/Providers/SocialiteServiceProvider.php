<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Laravel\Socialite\SocialiteManager;

class SocialiteServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->singleton('Lara<PERSON>\Socialite\Contracts\Factory', function ($app) {
            return new SocialiteManager($app);
        });
    }

    public function boot()
    {
        //
    }
}