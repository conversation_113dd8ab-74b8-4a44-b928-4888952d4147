<?php

namespace App\Notifications;

use App\Models\Confession;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class ConfessionCommented extends Notification
{
    use Queueable;

    public function __construct(public Confession $confession, public User $commenter) {}

    public function via($notifiable): array
    {
        return ['database'];
    }

    public function toArray($notifiable): array
    {
        return [
            'message' => $this->commenter->name . ' commented on your confession',
            'confession_id' => $this->confession->id,
            'commenter_id' => $this->commenter->id,
            'commenter_name' => $this->commenter->name,
        ];
    }
}