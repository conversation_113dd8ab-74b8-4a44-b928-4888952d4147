<?php

namespace App\Notifications;

use App\Models\Confession;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class ConfessionLiked extends Notification
{
    use Queueable;

    public function __construct(public Confession $confession, public User $liker) {}

    public function via($notifiable): array
    {
        return ['database'];
    }

    public function toArray($notifiable): array
    {
        return [
            'message' => $this->liker->name . ' liked your confession',
            'confession_id' => $this->confession->id,
            'liker_id' => $this->liker->id,
            'liker_name' => $this->liker->name,
        ];
    }
}