<?php

use App\Http\Controllers\ConfessionController;
use App\Http\Controllers\LikeController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

Route::get('/', [ConfessionController::class, 'index'])->name('home');
Route::get('/home', [ConfessionController::class, 'index'])->name('home.alt');

Route::get('/dashboard', function () {
    $stats = [
        'confessions_count' => \App\Models\Confession::count(),
        'user_confessions' => \App\Models\Confession::where('user_id', auth()->id())->count(),
        'voice_rooms_count' => \App\Models\VoiceRoom::where('is_active', true)->count(),
        'total_likes' => \App\Models\Like::count()
    ];
    return view('dashboard', compact('stats'));
})->middleware(['auth', 'verified'])->name('dashboard');

// Confession routes
Route::resource('confessions', ConfessionController::class);
Route::get('/confessions/random/surprise', [ConfessionController::class, 'random'])
    ->name('confessions.random');
Route::get('/confessions/ai-generator', [ConfessionController::class, 'showAIGenerator'])
    ->name('confessions.ai-generator');
Route::post('/confessions/generate', [ConfessionController::class, 'generateConfession'])
    ->name('confessions.generate');

// Like routes
Route::post('/confessions/{confession}/like', [LikeController::class, 'toggle'])
    ->name('confessions.like');

// Reaction routes
use App\Http\Controllers\ReactionController;
Route::post('/confessions/{confession}/react', [ReactionController::class, 'toggle'])
    ->name('confessions.react');

// Comment routes
use App\Http\Controllers\CommentController;
Route::post('/confessions/{confession}/comments', [CommentController::class, 'store'])
    ->name('comments.store');
Route::delete('/comments/{comment}', [CommentController::class, 'destroy'])
    ->name('comments.destroy');

// Google OAuth routes
use App\Http\Controllers\GoogleController;
Route::get('/auth/google', [GoogleController::class, 'redirect'])->name('google.redirect');
Route::get('/auth/google/callback', [GoogleController::class, 'callback'])->name('google.callback');

// Follow routes
use App\Http\Controllers\FollowController;
Route::post('/users/{user}/follow', [FollowController::class, 'toggle'])
    ->name('users.follow');
Route::get('/users/{user}/followers', [FollowController::class, 'followers'])
    ->name('users.followers');
Route::get('/users/{user}/following', [FollowController::class, 'following'])
    ->name('users.following');

// Voice Room routes
use App\Http\Controllers\VoiceRoomController;
Route::resource('voice-rooms', VoiceRoomController::class);

// Notification routes
use App\Http\Controllers\NotificationController;
Route::middleware('auth')->group(function () {
    Route::get('/notifications', [NotificationController::class, 'index'])->name('notifications.index');
    Route::post('/notifications/{id}/read', [NotificationController::class, 'markAsRead'])->name('notifications.read');
    Route::post('/notifications/read-all', [NotificationController::class, 'markAllAsRead'])->name('notifications.readAll');
    Route::get('/notifications/unread-count', [NotificationController::class, 'getUnreadCount'])->name('notifications.unreadCount');
});

// Theme routes
use App\Http\Controllers\ThemeController;
Route::post('/theme/toggle', [ThemeController::class, 'toggle'])->name('theme.toggle')->middleware('auth');

// User Profile routes
use App\Http\Controllers\UserProfileController;
Route::get('/users/{user}', [UserProfileController::class, 'show'])->name('users.profile');

// Message routes
use App\Http\Controllers\MessageController;
Route::middleware('auth')->group(function () {
    Route::get('/messages', [MessageController::class, 'index'])->name('messages.index');
    Route::get('/messages/{user}', [MessageController::class, 'show'])->name('messages.show');
    Route::post('/messages/{user}', [MessageController::class, 'store'])->name('messages.store');
    Route::post('/messages/{user}/share/{confession}', [MessageController::class, 'shareConfession'])->name('messages.share');
    Route::get('/messages/unread/count', [MessageController::class, 'getUnreadCount'])->name('messages.unread');
});

// Friendship routes
use App\Http\Controllers\FriendshipController;
Route::middleware('auth')->group(function () {
    Route::get('/friends', [FriendshipController::class, 'index'])->name('friends.index');
    Route::post('/friends/{user}/request', [FriendshipController::class, 'sendRequest'])->name('friends.request');
    Route::post('/friends/{friendship}/accept', [FriendshipController::class, 'acceptRequest'])->name('friends.accept');
    Route::post('/friends/{friendship}/reject', [FriendshipController::class, 'rejectRequest'])->name('friends.reject');
    Route::delete('/friends/{user}', [FriendshipController::class, 'removeFriend'])->name('friends.remove');
});

// Bookmark routes
use App\Http\Controllers\BookmarkController;
Route::middleware('auth')->group(function () {
    Route::get('/bookmarks', [BookmarkController::class, 'index'])->name('bookmarks.index');
    Route::post('/confessions/{confession}/bookmark', [BookmarkController::class, 'toggle'])->name('bookmarks.toggle');
    Route::post('/bookmarks/collections', [BookmarkController::class, 'createCollection'])->name('bookmarks.collections.create');
    Route::delete('/bookmarks/collections', [BookmarkController::class, 'deleteCollection'])->name('bookmarks.collections.delete');
});

// Feed routes
use App\Http\Controllers\FeedController;
Route::get('/feed', [FeedController::class, 'index'])->name('feed.index');
Route::get('/feed/trending', [FeedController::class, 'trending'])->name('feed.trending');
Route::get('/feed/wildest', [FeedController::class, 'wildest'])->name('feed.wildest');
Route::get('/feed/categories', [FeedController::class, 'categories'])->name('feed.categories');
Route::get('/feed/category/{category}', [FeedController::class, 'category'])->name('feed.category');
Route::get('/feed/search', [FeedController::class, 'search'])->name('feed.search');
Route::get('/feed/for-you', [FeedController::class, 'forYou'])->name('feed.for-you')->middleware('auth');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
