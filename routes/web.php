<?php

use App\Http\Controllers\ConfessionController;
use App\Http\Controllers\LikeController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

Route::get('/', [ConfessionController::class, 'index'])->name('home');
Route::get('/home', [ConfessionController::class, 'index'])->name('home.alt');

Route::get('/dashboard', function () {
    $stats = [
        'confessions_count' => \App\Models\Confession::count(),
        'user_confessions' => \App\Models\Confession::where('user_id', auth()->id())->count(),
        'voice_rooms_count' => \App\Models\VoiceRoom::where('is_active', true)->count(),
        'total_likes' => \App\Models\Like::count()
    ];
    return view('dashboard', compact('stats'));
})->middleware(['auth', 'verified'])->name('dashboard');

// Confession routes
Route::resource('confessions', ConfessionController::class);
Route::get('/confessions/random/surprise', [ConfessionController::class, 'random'])
    ->name('confessions.random');
Route::get('/confessions/ai-generator', [ConfessionController::class, 'showAIGenerator'])
    ->name('confessions.ai-generator');
Route::post('/confessions/generate', [ConfessionController::class, 'generateConfession'])
    ->name('confessions.generate');

// Like routes
Route::post('/confessions/{confession}/like', [LikeController::class, 'toggle'])
    ->name('confessions.like');

// Reaction routes
use App\Http\Controllers\ReactionController;
Route::post('/confessions/{confession}/react', [ReactionController::class, 'toggle'])
    ->name('confessions.react');

// Comment routes
use App\Http\Controllers\CommentController;
Route::post('/confessions/{confession}/comments', [CommentController::class, 'store'])
    ->name('comments.store');
Route::delete('/comments/{comment}', [CommentController::class, 'destroy'])
    ->name('comments.destroy');

// Google OAuth routes
use App\Http\Controllers\GoogleController;
Route::get('/auth/google', [GoogleController::class, 'redirect'])->name('google.redirect');
Route::get('/auth/google/callback', [GoogleController::class, 'callback'])->name('google.callback');

// Follow routes
use App\Http\Controllers\FollowController;
Route::post('/users/{user}/follow', [FollowController::class, 'toggle'])
    ->name('users.follow');
Route::get('/users/{user}/followers', [FollowController::class, 'followers'])
    ->name('users.followers');
Route::get('/users/{user}/following', [FollowController::class, 'following'])
    ->name('users.following');

// Voice Room routes
use App\Http\Controllers\VoiceRoomController;
Route::resource('voice-rooms', VoiceRoomController::class);

// Voice Room participant management routes
Route::middleware('auth')->group(function () {
    Route::post('/voice-rooms/{voiceRoom}/join', [VoiceRoomController::class, 'join'])
        ->name('voice-rooms.join');
    Route::post('/voice-rooms/{voiceRoom}/leave', [VoiceRoomController::class, 'leave'])
        ->name('voice-rooms.leave');
    Route::post('/voice-rooms/{voiceRoom}/toggle-mute', [VoiceRoomController::class, 'toggleMute'])
        ->name('voice-rooms.toggle-mute');
    Route::post('/voice-rooms/{voiceRoom}/mute/{user}', [VoiceRoomController::class, 'muteParticipant'])
        ->name('voice-rooms.mute-participant');
    Route::post('/voice-rooms/{voiceRoom}/kick/{user}', [VoiceRoomController::class, 'kickParticipant'])
        ->name('voice-rooms.kick-participant');
    Route::get('/voice-rooms/{voiceRoom}/invitable-friends', [VoiceRoomController::class, 'getInvitableFriends'])
        ->name('voice-rooms.invitable-friends');
    Route::post('/voice-rooms/{voiceRoom}/invite-friends', [VoiceRoomController::class, 'inviteFriends'])
        ->name('voice-rooms.invite-friends');
    Route::get('/voice-rooms/{voiceRoom}/participants', [VoiceRoomController::class, 'getParticipants'])
        ->name('voice-rooms.participants');

    // Voice room chat routes
    Route::post('/voice-rooms/{voiceRoom}/messages', [VoiceRoomController::class, 'sendMessage'])
        ->name('voice-rooms.send-message');
    Route::get('/voice-rooms/{voiceRoom}/messages', [VoiceRoomController::class, 'getMessages'])
        ->name('voice-rooms.get-messages');
    Route::delete('/voice-rooms/{voiceRoom}/messages/{message}', [VoiceRoomController::class, 'deleteMessage'])
        ->name('voice-rooms.delete-message');
    Route::patch('/voice-rooms/{voiceRoom}/messages/{message}', [VoiceRoomController::class, 'editMessage'])
        ->name('voice-rooms.edit-message');

    // WebRTC signaling routes
    Route::post('/voice-rooms/{voiceRoom}/webrtc/signal', [VoiceRoomController::class, 'sendSignal'])
        ->name('voice-rooms.send-signal');
    Route::get('/voice-rooms/{voiceRoom}/webrtc/signals', [VoiceRoomController::class, 'getSignals'])
        ->name('voice-rooms.get-signals');
    Route::get('/voice-rooms/{voiceRoom}/webrtc/participants', [VoiceRoomController::class, 'getWebRTCParticipants'])
        ->name('voice-rooms.webrtc-participants');
});

// Notification routes
use App\Http\Controllers\NotificationController;
Route::middleware('auth')->group(function () {
    Route::get('/notifications', [NotificationController::class, 'index'])->name('notifications.index');
    Route::post('/notifications/{id}/read', [NotificationController::class, 'markAsRead'])->name('notifications.read');
    Route::post('/notifications/read-all', [NotificationController::class, 'markAllAsRead'])->name('notifications.readAll');
    Route::get('/notifications/unread-count', [NotificationController::class, 'getUnreadCount'])->name('notifications.unreadCount');
});

// Theme routes
use App\Http\Controllers\ThemeController;
Route::post('/theme/toggle', [ThemeController::class, 'toggle'])->name('theme.toggle')->middleware('auth');

// User Profile routes
use App\Http\Controllers\UserProfileController;
Route::get('/users/{user}', [UserProfileController::class, 'show'])->name('users.profile');

// Message routes
use App\Http\Controllers\MessageController;
Route::middleware('auth')->group(function () {
    Route::get('/messages', [MessageController::class, 'index'])->name('messages.index');
    Route::get('/messages/{user}', [MessageController::class, 'show'])->name('messages.show');
    Route::post('/messages/{user}', [MessageController::class, 'store'])->name('messages.store');
    Route::post('/messages/{user}/share/{confession}', [MessageController::class, 'shareConfession'])->name('messages.share');
    Route::get('/messages/unread/count', [MessageController::class, 'getUnreadCount'])->name('messages.unread');
});

// Friendship routes
use App\Http\Controllers\FriendshipController;
Route::middleware('auth')->group(function () {
    Route::get('/friends', [FriendshipController::class, 'index'])->name('friends.index');
    Route::post('/friends/{user}/request', [FriendshipController::class, 'sendRequest'])->name('friends.request');
    Route::post('/friends/{friendship}/accept', [FriendshipController::class, 'acceptRequest'])->name('friends.accept');
    Route::post('/friends/{friendship}/reject', [FriendshipController::class, 'rejectRequest'])->name('friends.reject');
    Route::delete('/friends/{user}', [FriendshipController::class, 'removeFriend'])->name('friends.remove');
});

// Bookmark routes
use App\Http\Controllers\BookmarkController;
Route::middleware('auth')->group(function () {
    Route::get('/bookmarks', [BookmarkController::class, 'index'])->name('bookmarks.index');
    Route::post('/confessions/{confession}/bookmark', [BookmarkController::class, 'toggle'])->name('bookmarks.toggle');
    Route::post('/bookmarks/collections', [BookmarkController::class, 'createCollection'])->name('bookmarks.collections.create');
    Route::delete('/bookmarks/collections', [BookmarkController::class, 'deleteCollection'])->name('bookmarks.collections.delete');
});

// Feed routes
use App\Http\Controllers\FeedController;
Route::get('/feed', [FeedController::class, 'index'])->name('feed.index');
Route::get('/feed/trending', [FeedController::class, 'trending'])->name('feed.trending');
Route::get('/feed/wildest', [FeedController::class, 'wildest'])->name('feed.wildest');
Route::get('/feed/categories', [FeedController::class, 'categories'])->name('feed.categories');
Route::get('/feed/category/{category}', [FeedController::class, 'category'])->name('feed.category');
Route::get('/feed/search', [FeedController::class, 'search'])->name('feed.search');
Route::get('/feed/for-you', [FeedController::class, 'forYou'])->name('feed.for-you')->middleware('auth');

// Make current user admin (temporary route for setup)
Route::get('/make-admin', function() {
    if (Auth::check()) {
        Auth::user()->update([
            'is_admin' => true,
            'user_role' => 'super_admin'
        ]);
        return redirect('/admin')->with('success', 'You are now an admin!');
    }
    return redirect('/login');
})->middleware('auth');

// Admin routes
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\UserController as AdminUserController;
use App\Http\Controllers\Admin\ModerationController;

Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/system/health', [DashboardController::class, 'systemHealth'])->name('system.health');

    // User Management
    Route::resource('users', AdminUserController::class);
    Route::post('/users/{user}/ban', [AdminUserController::class, 'ban'])->name('users.ban');
    Route::post('/users/{user}/unban', [AdminUserController::class, 'unban'])->name('users.unban');
    Route::post('/users/{user}/verify', [AdminUserController::class, 'verify'])->name('users.verify');
    Route::post('/users/{user}/unverify', [AdminUserController::class, 'unverify'])->name('users.unverify');
    Route::post('/users/bulk-action', [AdminUserController::class, 'bulkAction'])->name('users.bulk-action');

    // Content Moderation
    Route::get('/moderation/reports', [ModerationController::class, 'reports'])->name('moderation.reports');
    Route::get('/moderation/reports/{report}', [ModerationController::class, 'showReport'])->name('moderation.report');
    Route::patch('/moderation/reports/{report}', [ModerationController::class, 'updateReport'])->name('moderation.report.update');
    Route::get('/moderation/confessions', [ModerationController::class, 'confessions'])->name('moderation.confessions');
    Route::get('/moderation/confessions/{confession}', [ModerationController::class, 'showConfession'])->name('moderation.confession');
    Route::patch('/moderation/confessions/{confession}', [ModerationController::class, 'updateConfession'])->name('moderation.confession.update');
    Route::post('/moderation/confessions/bulk', [ModerationController::class, 'bulkModeration'])->name('moderation.confessions.bulk');

    // AI Management
    Route::get('/ai', [App\Http\Controllers\Admin\AiController::class, 'index'])->name('ai.index');
    Route::get('/ai/usage', [App\Http\Controllers\Admin\AiController::class, 'usage'])->name('ai.usage');
    Route::get('/ai/costs', [App\Http\Controllers\Admin\AiController::class, 'costs'])->name('ai.costs');
    Route::get('/ai/analytics', [App\Http\Controllers\Admin\AiController::class, 'analytics'])->name('ai.analytics');
    Route::get('/ai/settings', [App\Http\Controllers\Admin\AiController::class, 'settings'])->name('ai.settings');
    Route::patch('/ai/settings', [App\Http\Controllers\Admin\AiController::class, 'updateSettings'])->name('ai.settings.update');
    Route::get('/ai/templates', [App\Http\Controllers\Admin\AiController::class, 'templates'])->name('ai.templates');
    Route::get('/ai/templates/create', [App\Http\Controllers\Admin\AiController::class, 'createTemplate'])->name('ai.templates.create');
    Route::post('/ai/templates', [App\Http\Controllers\Admin\AiController::class, 'storeTemplate'])->name('ai.templates.store');
    Route::get('/ai/templates/{template}/edit', [App\Http\Controllers\Admin\AiController::class, 'editTemplate'])->name('ai.templates.edit');
    Route::patch('/ai/templates/{template}', [App\Http\Controllers\Admin\AiController::class, 'updateTemplate'])->name('ai.templates.update');
    Route::post('/ai/templates/{template}/test', [App\Http\Controllers\Admin\AiController::class, 'testTemplate'])->name('ai.templates.test');

    // Analytics
    Route::get('/analytics', [App\Http\Controllers\Admin\AnalyticsController::class, 'index'])->name('analytics.index');
    Route::get('/analytics/users', [App\Http\Controllers\Admin\AnalyticsController::class, 'users'])->name('analytics.users');
    Route::get('/analytics/content', [App\Http\Controllers\Admin\AnalyticsController::class, 'content'])->name('analytics.content');
    Route::get('/analytics/growth', [App\Http\Controllers\Admin\AnalyticsController::class, 'growth'])->name('analytics.growth');
    Route::get('/analytics/realtime', [App\Http\Controllers\Admin\AnalyticsController::class, 'realtime'])->name('analytics.realtime');
    Route::get('/analytics/export', [App\Http\Controllers\Admin\AnalyticsController::class, 'export'])->name('analytics.export');

    // Security & Moderation
    Route::get('/security', [App\Http\Controllers\Admin\SecurityController::class, 'index'])->name('security.index');
    Route::get('/security/events', [App\Http\Controllers\Admin\SecurityController::class, 'events'])->name('security.events');
    Route::get('/security/moderation', [App\Http\Controllers\Admin\SecurityController::class, 'moderation'])->name('security.moderation');
    Route::post('/security/events/{event}/resolve', [App\Http\Controllers\Admin\SecurityController::class, 'resolveEvent'])->name('security.events.resolve');
    Route::post('/security/events/{event}/false-positive', [App\Http\Controllers\Admin\SecurityController::class, 'markFalsePositive'])->name('security.events.false-positive');
    Route::post('/security/moderation/{moderation}/approve', [App\Http\Controllers\Admin\SecurityController::class, 'approveModerationContent'])->name('security.moderation.approve');
    Route::post('/security/moderation/{moderation}/reject', [App\Http\Controllers\Admin\SecurityController::class, 'rejectModerationContent'])->name('security.moderation.reject');
    Route::post('/security/moderation/{moderation}/escalate', [App\Http\Controllers\Admin\SecurityController::class, 'escalateModeration'])->name('security.moderation.escalate');
    Route::get('/security/blocked-ips', [App\Http\Controllers\Admin\SecurityController::class, 'blockedIPs'])->name('security.blocked-ips');
    Route::get('/security/suspended-users', [App\Http\Controllers\Admin\SecurityController::class, 'suspendedUsers'])->name('security.suspended-users');
    Route::post('/security/users/{user}/unsuspend', [App\Http\Controllers\Admin\SecurityController::class, 'unsuspendUser'])->name('security.users.unsuspend');
    Route::post('/security/users/{user}/unban', [App\Http\Controllers\Admin\SecurityController::class, 'unbanUser'])->name('security.users.unban');
    Route::get('/security/settings', [App\Http\Controllers\Admin\SecurityController::class, 'settings'])->name('security.settings');
    Route::post('/security/settings', [App\Http\Controllers\Admin\SecurityController::class, 'updateSettings'])->name('security.settings.update');
    Route::get('/security/analytics', [App\Http\Controllers\Admin\SecurityController::class, 'analytics'])->name('security.analytics');
    Route::get('/security/export', [App\Http\Controllers\Admin\SecurityController::class, 'export'])->name('security.export');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
