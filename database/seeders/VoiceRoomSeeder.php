<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\VoiceRoom;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class VoiceRoomSeeder extends Seeder
{
    public function run(): void
    {
        $user = User::first(); // Get the test user created in ConfessionSeeder
        
        if ($user) {
            $rooms = [
                [
                    'name' => 'Late Night Confessions',
                    'description' => 'Share your deepest thoughts in a safe space',
                    'host_id' => $user->id,
                    'category' => 'Confessions',
                    'max_participants' => 10,
                    'current_participants' => 3,
                    'is_private' => false,
                    'is_active' => true
                ],
                [
                    'name' => 'Study Together',
                    'description' => 'Virtual study session with background music',
                    'host_id' => $user->id,
                    'category' => 'Study',
                    'max_participants' => 15,
                    'current_participants' => 7,
                    'is_private' => false,
                    'is_active' => true
                ],
                [
                    'name' => 'Gaming Chat',
                    'description' => 'Discuss your favorite games and find teammates',
                    'host_id' => $user->id,
                    'category' => 'Gaming',
                    'max_participants' => 20,
                    'current_participants' => 12,
                    'is_private' => false,
                    'is_active' => true
                ],
                [
                    'name' => 'Support Circle',
                    'description' => 'A safe space for emotional support and encouragement',
                    'host_id' => $user->id,
                    'category' => 'Support',
                    'max_participants' => 8,
                    'current_participants' => 4,
                    'is_private' => false,
                    'is_active' => true
                ]
            ];
            
            foreach ($rooms as $room) {
                VoiceRoom::create($room);
            }
        }
    }
}
