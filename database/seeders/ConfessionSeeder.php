<?php

namespace Database\Seeders;

use App\Models\Confession;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ConfessionSeeder extends Seeder
{
    public function run(): void
    {
        // Create a test user first
        $user = User::create([
            'name' => 'Test User',
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);

        // Create sample confessions
        $confessions = [
            [
                'content' => 'I sometimes pretend to be busy at work when I\'m actually just browsing social media. I feel guilty about it but I can\'t seem to stop.',
                'category' => 'Work',
                'is_anonymous' => false,
                'user_id' => $user->id
            ],
            [
                'content' => 'I\'ve been in love with my best friend for 3 years but I\'m too scared to tell them because I don\'t want to ruin our friendship.',
                'category' => 'Love',
                'is_anonymous' => true,
                'user_id' => null
            ],
            [
                'content' => 'I ate the last piece of cake and blamed it on my roommate. I still feel bad about it.',
                'category' => 'Personal',
                'is_anonymous' => true,
                'user_id' => null
            ],
            [
                'content' => 'I\'ve been learning to play guitar in secret for 6 months because I want to surprise my family at Christmas.',
                'category' => 'Family',
                'is_anonymous' => false,
                'user_id' => $user->id
            ],
            [
                'content' => 'Sometimes I talk to my plants and I think they actually listen to me. It makes me feel less lonely.',
                'category' => 'Personal',
                'is_anonymous' => true,
                'user_id' => null
            ]
        ];

        foreach ($confessions as $confession) {
            Confession::create($confession);
        }
    }
}
