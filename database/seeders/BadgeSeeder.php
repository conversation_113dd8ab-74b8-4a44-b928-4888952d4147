<?php

namespace Database\Seeders;

use App\Models\Badge;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BadgeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $badges = [
            [
                'name' => 'First Steps',
                'description' => 'Shared your first confession',
                'icon' => '🌟',
                'color' => 'bg-yellow-500',
                'condition' => 'first_confession'
            ],
            [
                'name' => 'Popular',
                'description' => 'Received 10 likes on confessions',
                'icon' => '❤️',
                'color' => 'bg-red-500',
                'condition' => '10_likes'
            ],
            [
                'name' => 'Storyteller',
                'description' => 'Shared 5 confessions',
                'icon' => '📖',
                'color' => 'bg-blue-500',
                'condition' => '5_confessions'
            ],
            [
                'name' => 'Supportive',
                'description' => 'Left 10 helpful comments',
                'icon' => '🤝',
                'color' => 'bg-green-500',
                'condition' => '10_comments'
            ],
            [
                'name' => 'Voice Leader',
                'description' => 'Created your first voice room',
                'icon' => '🎤',
                'color' => 'bg-purple-500',
                'condition' => 'first_voice_room'
            ],
            [
                'name' => 'Community Star',
                'description' => 'Received 50 total reactions',
                'icon' => '⭐',
                'color' => 'bg-indigo-500',
                'condition' => '50_reactions'
            ]
        ];

        foreach ($badges as $badge) {
            Badge::create($badge);
        }
    }
}
