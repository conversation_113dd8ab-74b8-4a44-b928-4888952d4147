<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\AiSettings;
use App\Models\PromptTemplate;

class AiDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default AI settings
        AiSettings::createDefaultSettings();
        
        // Create default prompt templates
        PromptTemplate::createDefaultTemplates();
        
        $this->command->info('AI settings and templates seeded successfully!');
    }
}
