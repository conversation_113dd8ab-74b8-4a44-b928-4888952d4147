<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('voice_room_participants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('voice_room_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->timestamp('joined_at')->useCurrent();
            $table->timestamp('left_at')->nullable();
            $table->enum('status', ['active', 'muted', 'speaking', 'away', 'kicked'])->default('active');
            $table->boolean('is_muted_by_host')->default(false);
            $table->boolean('is_self_muted')->default(false);
            $table->boolean('is_speaking')->default(false);
            $table->enum('role', ['participant', 'moderator', 'host'])->default('participant');
            $table->json('permissions')->nullable(); // Custom permissions for moderators
            $table->timestamps();

            $table->unique(['voice_room_id', 'user_id']);
            $table->index(['voice_room_id', 'status']);
            $table->index(['user_id', 'joined_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('voice_room_participants');
    }
};
