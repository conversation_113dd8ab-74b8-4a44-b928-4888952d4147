<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('confessions', function (Blueprint $table) {
            $table->boolean('is_nsfw')->default(false);
            $table->enum('content_type', ['short', 'long'])->default('long');
            $table->integer('bookmark_count')->default(0);
            $table->integer('share_count')->default(0);
            $table->json('tags')->nullable();
            $table->boolean('is_ai_generated')->default(false);
            $table->boolean('friends_only')->default(false);
            $table->text('ai_prompt')->nullable(); // Store the prompt used for AI generation
            $table->string('tone')->nullable(); // Store tone preference for AI
            
            $table->index(['is_nsfw', 'created_at']);
            $table->index(['content_type', 'created_at']);
            $table->index(['friends_only', 'user_id']);
        });
    }

    public function down(): void
    {
        Schema::table('confessions', function (Blueprint $table) {
            $table->dropColumn([
                'is_nsfw', 'content_type', 'bookmark_count', 'share_count', 
                'tags', 'is_ai_generated', 'friends_only', 'ai_prompt', 'tone'
            ]);
        });
    }
};
