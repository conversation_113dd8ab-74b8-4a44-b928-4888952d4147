<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('content_moderations', function (Blueprint $table) {
            $table->id();
            $table->string('content_type');
            $table->unsignedBigInteger('content_id');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('moderation_type')->nullable();
            $table->decimal('ai_score', 3, 2)->nullable();
            $table->decimal('human_score', 3, 2)->nullable();
            $table->enum('status', ['pending', 'approved', 'rejected', 'flagged', 'escalated'])->default('pending');
            $table->text('reason')->nullable();
            $table->string('action_taken')->nullable();
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('reviewed_at')->nullable();
            $table->json('metadata')->nullable();
            $table->boolean('auto_moderated')->default(false);
            $table->timestamps();

            $table->index(['content_type', 'content_id']);
            $table->index(['status', 'created_at']);
            $table->index(['moderation_type', 'created_at']);
            $table->index(['ai_score', 'created_at']);
            $table->index(['auto_moderated', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('content_moderations');
    }
};
