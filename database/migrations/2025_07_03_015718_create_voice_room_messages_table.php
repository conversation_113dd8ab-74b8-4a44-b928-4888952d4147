<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('voice_room_messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('voice_room_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('message');
            $table->enum('message_type', ['text', 'system', 'emoji', 'announcement'])->default('text');
            $table->boolean('is_anonymous')->default(false);
            $table->json('metadata')->nullable(); // For storing additional data like reactions, mentions, etc.
            $table->timestamp('edited_at')->nullable();
            $table->boolean('is_deleted')->default(false);
            $table->timestamps();

            $table->index(['voice_room_id', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['voice_room_id', 'is_deleted', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('voice_room_messages');
    }
};
