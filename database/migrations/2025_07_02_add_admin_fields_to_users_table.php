<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('is_admin')->default(false);
            $table->boolean('is_banned')->default(false);
            $table->timestamp('banned_until')->nullable();
            $table->text('ban_reason')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->timestamp('last_active_at')->nullable();
            $table->json('admin_notes')->nullable();
            $table->string('user_role')->default('user'); // user, moderator, admin, super_admin
            
            $table->index(['is_admin', 'is_banned']);
            $table->index(['user_role', 'is_banned']);
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'is_admin', 'is_banned', 'banned_until', 'ban_reason', 
                'is_verified', 'last_active_at', 'admin_notes', 'user_role'
            ]);
        });
    }
};
