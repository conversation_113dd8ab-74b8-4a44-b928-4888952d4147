<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('chat_rooms', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('creator_id')->constrained('users')->onDelete('cascade');
            $table->boolean('is_private')->default(false);
            $table->integer('max_members')->default(50);
            $table->string('category')->nullable();
            $table->boolean('is_nsfw')->default(false);
            $table->string('room_code')->unique()->nullable();
            $table->timestamps();

            $table->index(['category', 'is_private']);
            $table->index(['is_nsfw', 'is_private']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('chat_rooms');
    }
};
