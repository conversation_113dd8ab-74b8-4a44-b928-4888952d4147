<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('webrtc_signals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('voice_room_id')->constrained()->onDelete('cascade');
            $table->foreignId('from_user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('to_user_id')->nullable()->constrained('users')->onDelete('cascade'); // null for broadcast
            $table->string('signal_type'); // 'offer', 'answer', 'ice-candidate', 'join', 'leave'
            $table->json('signal_data');
            $table->boolean('processed')->default(false);
            $table->timestamps();

            $table->index(['voice_room_id', 'to_user_id', 'processed']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('webrtc_signals');
    }
};
