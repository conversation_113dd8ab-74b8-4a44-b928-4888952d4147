<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('bookmarks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('confession_id')->constrained()->onDelete('cascade');
            $table->string('collection_name')->default('default');
            $table->timestamps();

            $table->unique(['user_id', 'confession_id']);
            $table->index(['user_id', 'collection_name']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('bookmarks');
    }
};
